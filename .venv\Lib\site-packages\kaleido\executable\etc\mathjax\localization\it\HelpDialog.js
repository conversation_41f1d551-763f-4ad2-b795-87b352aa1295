/*************************************************************
 *
 *  MathJax/localization/it/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("it","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "Aiuto su MathJax",
          MathJax: "*MathJax* \u00E8 una libreria JavaScript che permette agli autori di includere formule matematiche nelle loro pagine web. Come lettore, non devi far nulla perch\u00E9 questo accada.",
          Browsers: "*Browser*: MathJax funziona con tutti i moderni browser inclusi IE6+, Firefox 3+, Chrome 0.2+, Safari 2+, Opera 9.6+ e gran parte di quelli per cellulare.",
          Menu: "*Menu Formule*: MathJax aggiunge un menu contestuale alle equazioni. Fai click col tasto destro del mouse oppure CTRL-click su una qualsiasi formula per accedere a tale menu.",
          ShowMath: "*Mostra formula come* ti permette di visualizzare il codice sorgente per il copia e incolla (in formato MathML o in quello originale).",
          Settings: "*Impostazioni* permette di controllare le caratteristiche di MathJax, come la grandezza delle formule e il meccanismo usato per mostrare le equazioni.",
          Language: "*Lingua* ti permette di selezionare la lingua usata da MathJax nei propri menu e nei messaggi d'avviso.",
          Zoom: "*Zoom formula*: se hai difficolt\u00E1 nella lettura di un'equazione, MathJax pu\u00F2 ingrandirla per permetterti di vederla meglio.",
          Accessibilty: "*Accessibilit\u00E1*: MathJax funzioner\u00E1 automaticamente con gli screen reader per rendere le formule accessibili a chi ha problemi di vista.",
          Fonts: "*Font*: MathJax user\u00E1 certi tipi di font se presenti sul tuo computer; altrimenti usera i web font. Sebbene non sia richiesto, font installati sul proprio computer velocizzeranno l'esecuzione di MathJax. Ti suggeriamo di installare se puoi gli [STIX font](%1).",
          CloseDialog: "Chiudi finestra di aiuto"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/it/HelpDialog.js");
