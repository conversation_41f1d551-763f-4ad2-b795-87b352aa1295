/*************************************************************
 *
 *  MathJax/localization/lt/FontWarnings.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("lt","FontWarnings",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          webFont: "\u0160iame tinklalapyje matematikos tekst\u0105 \u201EMathJax\u201C rodo tinkle saugomais \u0161riftais, kuriems \u012Fkelti reikia laiko. Teksto rinkim\u0105 galima greitinti, matematikos \u0161riftus \u012Fdiegus naudotojo kompiuteryje.",
          imageFonts: "\u201EMathJax\u201C taiko paveikslinius, o ne vietinius arba tinkle saugomus \u0161riftus. Matematikos tekstas vaizduojamas l\u0117\u010Diau, o spausdinant netenkama rai\u0161kos.",
          noFonts: "Neradusi matematikos tekstui skirt\u0173 \u0161rift\u0173, \u201EMathJax\u201C taiko bendruosius unikodo ra\u0161menis. Kai kurie ra\u0161menys gali b\u016Bti darkomi arba visai nerodomi.",
          webFonts: "Dauguma dabartini\u0173 nar\u0161ykli\u0173 leid\u017Eia gauti \u0161riftus i\u0161 tinklo. Tik\u0117tina, kad atnaujinus nar\u0161ykl\u0117s laid\u0105 arba esam\u0105 nar\u0161ykl\u0119 pakeitus kita, tinklalapyje ger\u0117t\u0173 matematikos teksto vaizdavimas.",
          fonts: "\u201EMathJax\u201C gali taikyti [STIX fonts](%1) arba [MathJax TeX fonts](%2). \u012Ediegus vien\u0105 i\u0161 min\u0117t\u0173 alternatyv\u0173, ger\u0117s \u201EMathJax\u201C veikimas.",
          STIXPage: "Tinklalapyje numatyti [STIX fonts](%1). \u012Ediegus min\u0117tus \u0161riftus, ger\u0117s \u201EMathJax\u201C veikimas.",
          TeXPage: "Tinklalapyje numatyti [MathJax TeX fonts](%1).  \u012Ediegus min\u0117tus \u0161riftus, ger\u0117s \u201EMathJax\u201C veikimas."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/lt/FontWarnings.js");
