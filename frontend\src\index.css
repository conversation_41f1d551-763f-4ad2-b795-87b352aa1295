/* Import Evos corporate font - Blinker */
@import url('https://fonts.googleapis.com/css2?family=Blinker:wght@100;200;300;400;600;700;800;900&display=swap');

body {
  margin: 0;
  font-family: 'Blinker', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.step-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  padding: 24px;
  margin-bottom: 20px;
}

.step-header {
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 16px;
  margin-bottom: 24px;
}

.step-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.step-description {
  font-size: 14px;
  color: #666;
  margin: 8px 0 0 0;
}

.upload-zone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.upload-zone:hover {
  border-color: #FF6B35;
}

.upload-zone.active {
  border-color: #FF6B35;
  background-color: #fff5f2;
}

.error-message {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 12px;
  border-radius: 4px;
  margin: 16px 0;
}

.success-message {
  color: #2e7d32;
  background-color: #e8f5e8;
  padding: 12px;
  border-radius: 4px;
  margin: 16px 0;
}

.warning-message {
  color: #f57c00;
  background-color: #fff3e0;
  padding: 12px;
  border-radius: 4px;
  margin: 16px 0;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.chart-container {
  margin: 20px 0;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin: 20px 0;
}

.metric-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  text-align: center;
}

.metric-value {
  font-size: 28px;
  font-weight: bold;
  color: #FF6B35;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 14px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.button-group {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.data-table {
  margin: 20px 0;
  overflow-x: auto;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin: 16px 0;
}

.progress-fill {
  height: 100%;
  background-color: #FF6B35;
  transition: width 0.3s ease;
}

.feature-importance-list {
  max-height: 400px;
  overflow-y: auto;
}

.feature-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.feature-name {
  font-weight: 500;
}

.feature-importance {
  color: #FF6B35;
  font-weight: bold;
}

/* Evos Corporate Styling */
.evos-header {
  background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
  color: white;
}

.evos-logo {
  height: 40px;
  margin-right: 16px;
  filter: brightness(0) invert(1); /* Make logo white for dark header */
}

.evos-tagline {
  font-size: 14px;
  font-weight: 300;
  opacity: 0.9;
  margin-left: 8px;
}

.evos-primary-color {
  color: #FF6B35 !important;
}

.evos-secondary-color {
  color: #1976d2 !important;
}

.evos-accent-bg {
  background-color: #FF6B35 !important;
}

.evos-gradient-bg {
  background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%) !important;
}

.evos-card {
  border-left: 4px solid #FF6B35;
  transition: all 0.3s ease;
}

.evos-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.15);
}

.evos-button {
  background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
  border: none;
  color: white;
  font-weight: 600;
  text-transform: none;
  border-radius: 8px;
  padding: 12px 24px;
  transition: all 0.3s ease;
}

.evos-button:hover {
  background: linear-gradient(135deg, #E55A2B 0%, #FF7043 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

@media (max-width: 768px) {
  .main-content {
    padding: 12px;
  }

  .step-container {
    padding: 16px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .button-group {
    flex-direction: column;
  }

  .evos-logo {
    height: 32px;
  }

  .evos-tagline {
    display: none;
  }
}
