/*************************************************************
 *
 *  MathJax/localization/kn/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("kn","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "\u0CAE\u0CBE\u0CA4\u0CCD \u0C9C\u0C95\u0CCD\u0CB7\u0CCD \u0CB8\u0CB9\u0CAF\u0CA4\u0CC6",
          MathJax: "\u0CAE\u0CBE\u0CA4\u0CCD \u0C9C\u0C95\u0CCD\u0CB7\u0CCD   \u0CAA\u0CC1\u0C9F \u0CB2\u0CC7\u0C96\u0C95\u0CB0\u0CC1 \u0CA4\u0CAE\u0CCD\u0CAE \u0CB5\u0CC6\u0CAC\u0CCD \u0CAA\u0CC1\u0C9F\u0C97\u0CB3 \u0C92\u0CB3\u0C97\u0CC6 \u0C97\u0CA3\u0CBF\u0CA4 \u0CB8\u0CC7\u0CB0\u0CBF\u0CB8\u0CB2\u0CC1 \u0C85\u0CA8\u0CC1\u0CAE\u0CA4\u0CBF\u0CB8\u0CC1\u0CB5 \u0C92\u0C82\u0CA6\u0CC1 \u0C9C\u0CBE\u0CB5\u0CBE\u0CB8\u0CCD\u0C95\u0CCD\u0CB0\u0CBF\u0CAA\u0CCD\u0C9F\u0CCD \u0C97\u0CCD\u0CB0\u0C82\u0CA5\u0CBE\u0CB2\u0CAF. \u0C92\u0C82\u0CA6\u0CC1 \u0CB0\u0CC0\u0CA1\u0CB0\u0CCD, \u0CA8\u0CC0\u0CB5\u0CC1 \u0C89\u0C82\u0C9F\u0CBE\u0C97\u0CC1\u0CB5 \u0CAE\u0CBE\u0CA1\u0CB2\u0CC1 \u0C8F\u0CA8\u0CC1 \u0C85\u0C97\u0CA4\u0CCD\u0CAF\u0CB5\u0CBF\u0CB2\u0CCD\u0CB2.",
          Browsers: "*\u0CAC\u0CCD\u0CB0\u0CCC\u0CB8\u0CB0\u0CCD\u0C97\u0CB3\u0CC1*: \u0CAE\u0CA0 Jax \u0C87\u0C82\u0C9F\u0CB0\u0CCD\u0CA8\u0CC6\u0C9F\u0CCD \u0C8E\u0C95\u0CCD\u0CB8\u0CCD\u0CAA\u0CCD\u0CB2\u0CCB\u0CB0\u0CB0\u0CCD 6 +, \u0CAB\u0CC8\u0CB0\u0CCD\u0CAB\u0CBE\u0C95\u0CCD\u0CB8\u0CCD 3 + \u0C95\u0CCD\u0CB0\u0CCB\u0CAE\u0CCD 0.2 +, \u0CB8\u0CAB\u0CBE\u0CB0\u0CBF 2 + \u0C92\u0CAA\u0CC6\u0CB0\u0CBE 9.6 + \u0CAE\u0CA4\u0CCD\u0CA4\u0CC1 \u0C85\u0CA4\u0CCD\u0CAF\u0C82\u0CA4 \u0CAE\u0CCA\u0CAC\u0CC8\u0CB2\u0CCD \u0CAC\u0CCD\u0CB0\u0CCC\u0CB8\u0CB0\u0CCD \u0CB8\u0CC7\u0CB0\u0CBF\u0CA6\u0C82\u0CA4\u0CC6 \u0C8E\u0CB2\u0CCD\u0CB2 \u0C86\u0CA7\u0CC1\u0CA8\u0CBF\u0C95 \u0CAC\u0CCD\u0CB0\u0CCC\u0CB8\u0CB0\u0CCD\u0C97\u0CB3\u0CC1 \u0C95\u0CC6\u0CB2\u0CB8 \u0CAE\u0CBE\u0CA1\u0CC1\u0CA4\u0CCD\u0CA4\u0CA6\u0CC6.",
          Zoom: "\u0CAE\u0CBE\u0CA4\u0CCD \u0C9C\u0CC2\u0CAE\u0CCD: \u0C92\u0C82\u0CA6\u0CC1 \u0CAA\u0C95\u0CCD\u0CB7  \u0C92\u0C82\u0CA6\u0CC1 \u0C8E\u0C95\u0CBC\u0CC1\u0C85\u0CA4\u0CBF\u0C92\u0CA8\u0CCD \u0C85\u0CA8\u0CCD\u0CA8\u0CC1 \u0CA8\u0CCB\u0CA1\u0CB2\u0CBF\u0C95\u0CCD\u0C95\u0CC6 \u0CA8\u0CBF\u0CAE\u0C97\u0CC6 \u0C89\u0CAA\u0CCD\u0CAA\u0CA6\u0CCD\u0CA6\u0CB0 \u0C86\u0C97\u0CC1\u0CA4 \u0C87\u0CA6\u0CCD\u0CA6\u0CBE\u0CB0\u0CC6, \u0CAE\u0CBE\u0CA4\u0CCD \u0C9C\u0C95\u0CCD\u0CB7\u0CCD \u0C85\u0CA6\u0CCD\u0CA6\u0CA8\u0CC1 \u0CA8\u0CBF\u0CAE\u0C97\u0CC6 \u0CB8\u0CB0\u0CBF \u0CA8\u0CCB\u0CA1\u0CB2\u0CBF\u0C95\u0CCD\u0C95\u0CC6 \u0CB8\u0CB9\u0CAF\u0CA4\u0CC6 \u0CAE\u0CBE\u0CA1\u0CB2\u0CBF\u0C95\u0CCD\u0C95\u0CC6 \u0C85\u0CA6\u0CA8\u0CCD\u0CA8\u0CC1 \u0CA6\u0CCA\u0CA1\u0CCD\u0CA1\u0CA6\u0CC1 \u0CAE\u0CBE\u0CA1\u0CB2\u0CBF\u0C95\u0CCD\u0C95\u0CC6 \u0C86\u0C97\u0CC1\u0CA4\u0CA6\u0CC6.",
          Fonts: "* \u0CAB\u0CBE\u0C82\u0C9F\u0CCD\u0C97\u0CB3\u0CC1 *: \u0C85\u0CB5\u0CB0\u0CC1 \u0CA8\u0CBF\u0CAE\u0CCD\u0CAE \u0C95\u0C82\u0CAA\u0CCD\u0CAF\u0CC2\u0C9F\u0CB0\u0CCD\u0CA8\u0CB2\u0CCD\u0CB2\u0CBF \u0C87\u0CA8\u0CCD\u0CB8\u0CCD\u0C9F\u0CBE\u0CB2\u0CCD \u0CB5\u0CC7\u0CB3\u0CC6 \u0CAE\u0CBE\u0CA4\u0CCD \u0C9C\u0C95\u0CCD\u0CB7\u0CCD   \u0C95\u0CC6\u0CB2\u0CB5\u0CC1 \u0C97\u0CA3\u0CBF\u0CA4 \u0CAB\u0CBE\u0C82\u0C9F\u0CCD\u0C97\u0CB3\u0CA8\u0CCD\u0CA8\u0CC1 \u0CAC\u0CB3\u0CB8\u0CC1\u0CA4\u0CCD\u0CA4\u0CA6\u0CC6; \u0C87\u0CB2\u0CCD\u0CB2\u0CA6\u0CBF\u0CA6\u0CCD\u0CA6\u0CB0\u0CC6, \u0C87\u0CA6\u0CC1 \u0CB5\u0CC6\u0CAC\u0CCD \u0C86\u0CA7\u0CBE\u0CB0\u0CBF\u0CA4 \u0CAB\u0CBE\u0C82\u0C9F\u0CCD\u0C97\u0CB3\u0CA8\u0CCD\u0CA8\u0CC1 \u0CAC\u0CB3\u0CB8\u0CC1\u0CA4\u0CCD\u0CA4\u0CA6\u0CC6. \u0C85\u0C97\u0CA4\u0CCD\u0CAF\u0CB5\u0CBF\u0CB2\u0CCD\u0CB2 \u0C86\u0CA6\u0CB0\u0CC2, \u0CB8\u0CCD\u0CA5\u0CB3\u0CC0\u0CAF\u0CB5\u0CBE\u0C97\u0CBF \u0C87\u0CA8\u0CCD\u0CB8\u0CCD\u0C9F\u0CBE\u0CB2\u0CCD \u0CAB\u0CBE\u0C82\u0C9F\u0CCD\u0C97\u0CB3\u0CC1 \u0C9F\u0CC8\u0CAA\u0CCD\u0CB8\u0CC6\u0C9F\u0CCD\u0C9F\u0CBF\u0C82\u0C97\u0CCD \u0CB5\u0CC7\u0C97\u0CB5\u0CA8\u0CCD\u0CA8\u0CC1. \u0CA8\u0CBE\u0CB5\u0CC1 [\u0CB8\u0CCD\u0C9F\u0CBF\u0C95\u0CCD\u0CB8\u0CCD \u0CAB\u0CBE\u0C82\u0C9F\u0CCD\u0C97\u0CB3\u0CC1](%1) \u0C85\u0CA8\u0CC1\u0CB8\u0CCD\u0CA5\u0CBE\u0CAA\u0CBF\u0CB8\u0CC1\u0CB5\u0CBE\u0C97 \u0CB8\u0CC2\u0C9A\u0CBF\u0CB8\u0CC1\u0CA4\u0CCD\u0CA4\u0CA6\u0CC6."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/kn/HelpDialog.js");
