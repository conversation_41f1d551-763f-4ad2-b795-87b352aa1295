/*************************************************************
 *
 *  MathJax/localization/da/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("da","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "Indl\u00E6ser webskrifftype %1",
          CantLoadWebFont: "Kan ikke indl\u00E6se webskrifttype %1",
          FirefoxCantLoadWebFont: "Firefox kan ikke indl\u00E6se webskrifttyper fra en fjernstyret v\u00E6rt",
          CantFindFontUsing: "Kunne ikke finde en gyldig skrifttype ved hj\u00E6lp af %1",
          WebFontsNotAvailable: "Webskrifttyper er ikke tilg\u00E6ngelig. Brug billede skrifttyper i stedet"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/da/HTML-CSS.js");
