/*************************************************************
 *
 *  MathJax/localization/oc/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("oc","MathMenu",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Show: "Afichar jos la forma",
          MathMLcode: "C\u00F2di MathML",
          OriginalMathML: "MathML d\u2019origina",
          TeXCommands: "Comandas TeX",
          AsciiMathInput: "entrada AsciiMathML",
          Original: "Format d'origina",
          ErrorMessage: "Messatge d\u2019error",
          Annotation: "Anotacion",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "MathML de contengut",
          OpenMath: "OpenMath",
          texHints: "Afichar las ajudas TeX en MathML",
          Settings: "Parametratges dels maths",
          ZoomTrigger: "Desenclavador de zoom",
          Hover: "Susv\u00F2l",
          Click: "Clic",
          DoubleClick: "Clic doble",
          NoZoom: "Pas de zoom",
          TriggerRequires: "Lo desenclavador necessita :",
          Option: "Opcion",
          Alt: "Alt",
          Command: "Comanda",
          Control: "Contrar\u00F2tle",
          Shift: "Shift",
          ZoomFactor: "Factor de grossiment",
          Renderer: "Rendut matematic",
          MPHandles: "Daissar MathPlayer gerir :",
          MenuEvents: "Eveniments de men\u00FA",
          MouseEvents: "Eveniments de mirga",
          FontPrefs: "Prefer\u00E9ncias de poli\u00E7a",
          ForHTMLCSS: "Per HTML-CSS :",
          Auto: "Auto",
          TeXLocal: "TeX (local)",
          TeXWeb: "TeX (web)",
          TeXImage: "TeX (imatge)",
          STIXLocal: "STIX (local)",
          STIXWeb: "STIX (web)",
          AsanaMathWeb: "Asana Math (web)",
          GyrePagellaWeb: "Gyre Pagella (web)",
          GyreTermesWeb: "Gyre Termes (web)",
          LatinModernWeb: "Latin Modern (web)",
          NeoEulerWeb: "Neo Euler (web)",
          ContextMenu: "Men\u00FA contextual",
          Browser: "Navigador",
          Locale: "Lenga",
          LoadLocale: "Cargar dempu\u00E8i l\u2019URL\u2026",
          About: "A prepaus de MathJax",
          Help: "Ajuda de MathJax",
          localTeXfonts: "utilizar las poli\u00E7as TeX localas",
          webTeXfonts: "utilizar las poli\u00E7as TeX del web",
          imagefonts: "utilizar las poli\u00E7as Imatge",
          localSTIXfonts: "utilizar las poli\u00E7as STIX localas",
          webSVGfonts: "utilizar las poli\u00E7as SVG del web",
          genericfonts: "utilizar las poli\u00E7as unicode genericas",
          wofforotffonts: "poli\u00E7as woff o otf",
          eotffonts: "poli\u00E7as eot",
          svgfonts: "poli\u00E7as svg",
          Close: "Tampar",
          Scale: "Metre totes los maths a l\u2019escala\u2026",
          CloseAboutDialog: "Tampar la b\u00F3stia de dial\u00F2g A prepaus de MathJax"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/oc/MathMenu.js");
