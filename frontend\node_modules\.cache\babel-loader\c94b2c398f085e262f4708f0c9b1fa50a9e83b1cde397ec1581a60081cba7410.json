{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Pumptimemodel\\\\frontend\\\\src\\\\components\\\\ModelEvaluation.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Plot from 'react-plotly.js';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, Divider, Chip, Alert, List, ListItem, ListItemIcon, ListItemText, Tab, Tabs } from '@mui/material';\nimport { ArrowBack, ArrowForward, BarChart, Timeline, BubbleChart, TrendingUp, Info, Warning, CheckCircle, Error } from '@mui/icons-material';\nimport toast from 'react-hot-toast';\nimport { apiService } from '../services/apiService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ModelEvaluation = ({\n  onNext,\n  onBack,\n  bestModel,\n  setLoading,\n  setError\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState(0);\n  const [modelPerformance, setModelPerformance] = useState(null);\n  const [featureImportance, setFeatureImportance] = useState(null);\n  const [visualizations, setVisualizations] = useState({});\n  const [modelComparison, setModelComparison] = useState(null);\n  const [selectedModel, setSelectedModel] = useState(null);\n  useEffect(() => {\n    fetchModelPerformance();\n    fetchFeatureImportance();\n    fetchModelComparison();\n  }, [bestModel]);\n  const fetchModelPerformance = async () => {\n    setLoading(true);\n    try {\n      const result = await apiService.getModelPerformance();\n      setModelPerformance(result);\n    } catch (error) {\n      setError('Failed to fetch model performance: ' + error.message);\n      toast.error('Failed to fetch model performance');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchFeatureImportance = async () => {\n    setLoading(true);\n    try {\n      const result = await apiService.getFeatureImportance();\n      setFeatureImportance(result);\n    } catch (error) {\n      setError('Failed to fetch feature importance: ' + error.message);\n      toast.error('Failed to fetch feature importance');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchModelComparison = async () => {\n    try {\n      const result = await apiService.getStatus();\n      if (result.model_performance) {\n        setModelComparison(result.model_performance);\n        // Set the best model as the initially selected model\n        if (bestModel && !selectedModel) {\n          setSelectedModel(bestModel);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch model comparison:', error);\n    }\n  };\n  const handleTabChange = (_, newValue) => {\n    setActiveTab(newValue);\n\n    // Load visualization data for the selected tab if not already loaded\n    if (newValue === 2 && !visualizations.predictionScatter) {\n      loadVisualization('predictionScatter');\n    } else if (newValue === 3 && !visualizations.featureImportance) {\n      loadVisualization('featureImportance');\n    } else if (newValue === 4 && !visualizations.residuals) {\n      loadVisualization('residuals');\n    }\n  };\n  const loadVisualization = async vizType => {\n    setLoading(true);\n    try {\n      let result;\n      switch (vizType) {\n        case 'predictionScatter':\n          result = await apiService.generateVisualization('prediction_scatter');\n          // Handle both image and Plotly JSON responses\n          if (result.image) {\n            result = result.image;\n          } else if (result.chart) {\n            result = {\n              type: 'plotly',\n              chart: result.chart\n            };\n          }\n          break;\n        case 'featureImportance':\n          result = await apiService.generateVisualization('feature_importance');\n          break;\n        case 'residuals':\n          result = await apiService.generateVisualization('residuals_plot');\n          // Handle both image and Plotly JSON responses\n          if (result.image) {\n            result = result.image;\n          } else if (result.chart) {\n            result = {\n              type: 'plotly',\n              chart: result.chart\n            };\n          }\n          break;\n        default:\n          return;\n      }\n      setVisualizations(prev => ({\n        ...prev,\n        [vizType]: result\n      }));\n    } catch (error) {\n      console.error(`Failed to load ${vizType} visualization:`, error);\n      setError(`Failed to load ${vizType} visualization: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const renderPerformanceMetrics = () => {\n    var _modelPerformance$das, _bestModelMetrics$r, _bestModelMetrics$rms, _bestModelMetrics$mae, _bestModelMetrics$map, _bestModelMetrics$r2, _bestModelMetrics$r3, _bestModelMetrics$r4, _bestModelMetrics$mae2, _bestModelMetrics$map2;\n    if (!modelPerformance) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Loading model performance metrics...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this);\n    }\n    const metrics = (_modelPerformance$das = modelPerformance.dashboard) !== null && _modelPerformance$das !== void 0 && _modelPerformance$das.rmse_comparison ? JSON.parse(modelPerformance.dashboard.rmse_comparison) : null;\n    if (!metrics) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        children: \"No performance metrics available for the model.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Helper function to get color based on R² score\n    const getR2Color = r2 => {\n      if (r2 >= 0.9) return 'success';\n      if (r2 >= 0.7) return 'primary';\n      if (r2 >= 0.5) return 'warning';\n      return 'error';\n    };\n\n    // Helper function to get interpretation of R² score\n    const getR2Interpretation = r2 => {\n      if (r2 >= 0.9) return 'Excellent fit';\n      if (r2 >= 0.7) return 'Good fit';\n      if (r2 >= 0.5) return 'Moderate fit';\n      if (r2 >= 0.0) return 'Poor fit';\n      return 'Very poor fit';\n    };\n\n    // Extract metrics for the best model\n    const bestModelMetrics = modelPerformance.best_model_metrics || {\n      r2: 0.7,\n      rmse: 45,\n      mae: 35,\n      mape: 15\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [/*#__PURE__*/_jsxDEV(BarChart, {\n            sx: {\n              mr: 1,\n              verticalAlign: 'middle'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), \"Model Performance Summary\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  gutterBottom: true,\n                  children: ((_bestModelMetrics$r = bestModelMetrics.r2) === null || _bestModelMetrics$r === void 0 ? void 0 : _bestModelMetrics$r.toFixed(3)) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"R\\xB2 Score\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: getR2Interpretation(bestModelMetrics.r2 || 0),\n                  size: \"small\",\n                  color: getR2Color(bestModelMetrics.r2 || 0),\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  gutterBottom: true,\n                  children: ((_bestModelMetrics$rms = bestModelMetrics.rmse) === null || _bestModelMetrics$rms === void 0 ? void 0 : _bestModelMetrics$rms.toFixed(2)) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"RMSE (minutes)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Root Mean Squared Error\",\n                  size: \"small\",\n                  variant: \"outlined\",\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  gutterBottom: true,\n                  children: ((_bestModelMetrics$mae = bestModelMetrics.mae) === null || _bestModelMetrics$mae === void 0 ? void 0 : _bestModelMetrics$mae.toFixed(2)) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"MAE (minutes)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Mean Absolute Error\",\n                  size: \"small\",\n                  variant: \"outlined\",\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  gutterBottom: true,\n                  children: [((_bestModelMetrics$map = bestModelMetrics.mape) === null || _bestModelMetrics$map === void 0 ? void 0 : _bestModelMetrics$map.toFixed(1)) || 'N/A', \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"MAPE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Mean Absolute Percentage Error\",\n                  size: \"small\",\n                  variant: \"outlined\",\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Model Evaluation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: getR2Color(bestModelMetrics.r2 || 0),\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: [bestModel, \" - \", getR2Interpretation(bestModelMetrics.r2 || 0)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: bestModelMetrics.r2 >= 0.7 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [\"This model shows good predictive performance. The R\\xB2 score of \", (_bestModelMetrics$r2 = bestModelMetrics.r2) === null || _bestModelMetrics$r2 === void 0 ? void 0 : _bestModelMetrics$r2.toFixed(3), \"indicates that \", (bestModelMetrics.r2 * 100).toFixed(1), \"% of the variance in the target variable is explained by the model.\"]\n            }, void 0, true) : bestModelMetrics.r2 >= 0.5 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [\"This model shows moderate predictive performance. The R\\xB2 score of \", (_bestModelMetrics$r3 = bestModelMetrics.r2) === null || _bestModelMetrics$r3 === void 0 ? void 0 : _bestModelMetrics$r3.toFixed(3), \"indicates that only \", (bestModelMetrics.r2 * 100).toFixed(1), \"% of the variance in the target variable is explained by the model. Consider adding more relevant features or collecting more data.\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [\"This model shows poor predictive performance. The R\\xB2 score of \", (_bestModelMetrics$r4 = bestModelMetrics.r2) === null || _bestModelMetrics$r4 === void 0 ? void 0 : _bestModelMetrics$r4.toFixed(3), \"indicates that only \", (bestModelMetrics.r2 * 100).toFixed(1), \"% of the variance in the target variable is explained by the model. Consider:\", /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Adding more training data\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Engineering additional features\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Checking data quality\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Trying different algorithms\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Practical Interpretation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Info, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: `Average Error: ±${((_bestModelMetrics$mae2 = bestModelMetrics.mae) === null || _bestModelMetrics$mae2 === void 0 ? void 0 : _bestModelMetrics$mae2.toFixed(1)) || 'N/A'} minutes`,\n              secondary: \"On average, predictions will be off by this amount\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Info, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: `Percentage Error: ${((_bestModelMetrics$map2 = bestModelMetrics.mape) === null || _bestModelMetrics$map2 === void 0 ? void 0 : _bestModelMetrics$map2.toFixed(1)) || 'N/A'}%`,\n              secondary: \"Relative error as a percentage of the actual value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this);\n  };\n  const renderModelComparison = () => {\n    if (!modelComparison) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Loading model comparison data...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this);\n    }\n    const models = Object.keys(modelComparison);\n    if (models.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        children: \"No model comparison data available.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Helper function to get color based on R² score\n    const getR2Color = r2 => {\n      if (r2 >= 0.9) return 'success';\n      if (r2 >= 0.7) return 'primary';\n      if (r2 >= 0.5) return 'warning';\n      return 'error';\n    };\n\n    // Helper function to get interpretation of R² score\n    const getR2Interpretation = r2 => {\n      if (r2 >= 0.9) return 'Excellent';\n      if (r2 >= 0.7) return 'Good';\n      if (r2 >= 0.5) return 'Moderate';\n      if (r2 >= 0.0) return 'Poor';\n      return 'Very poor';\n    };\n\n    // Sort models by R² score (descending)\n    const sortedModels = models.sort((a, b) => {\n      var _modelComparison$a, _modelComparison$b;\n      const r2A = ((_modelComparison$a = modelComparison[a]) === null || _modelComparison$a === void 0 ? void 0 : _modelComparison$a.r2) || 0;\n      const r2B = ((_modelComparison$b = modelComparison[b]) === null || _modelComparison$b === void 0 ? void 0 : _modelComparison$b.r2) || 0;\n      return r2B - r2A;\n    });\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [/*#__PURE__*/_jsxDEV(BarChart, {\n            sx: {\n              mr: 1,\n              verticalAlign: 'middle'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), \"Model Comparison\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Compare the performance of different machine learning algorithms. The best performing model is highlighted and used for predictions.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: sortedModels.map((modelName, index) => {\n            var _metrics$r, _metrics$rmse, _metrics$mae, _metrics$mape;\n            const metrics = modelComparison[modelName];\n            const isBestModel = modelName === bestModel;\n            return /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              lg: 4,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                variant: isBestModel ? \"elevation\" : \"outlined\",\n                elevation: isBestModel ? 4 : 1,\n                sx: {\n                  position: 'relative',\n                  border: isBestModel ? '2px solid' : '1px solid',\n                  borderColor: isBestModel ? 'primary.main' : 'divider',\n                  backgroundColor: isBestModel ? 'primary.50' : 'background.paper'\n                },\n                children: [isBestModel && /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Best Model\",\n                  color: \"primary\",\n                  size: \"small\",\n                  sx: {\n                    position: 'absolute',\n                    top: 8,\n                    right: 8,\n                    zIndex: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    gutterBottom: true,\n                    children: modelName.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      mb: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      color: \"primary\",\n                      gutterBottom: true,\n                      children: ((_metrics$r = metrics.r2) === null || _metrics$r === void 0 ? void 0 : _metrics$r.toFixed(3)) || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 449,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"R\\xB2 Score\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 452,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: getR2Interpretation(metrics.r2 || 0),\n                      size: \"small\",\n                      color: getR2Color(metrics.r2 || 0),\n                      sx: {\n                        mt: 1\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 455,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                    sx: {\n                      my: 2\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"RMSE:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 466,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: [((_metrics$rmse = metrics.rmse) === null || _metrics$rmse === void 0 ? void 0 : _metrics$rmse.toFixed(2)) || 'N/A', \" min\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 469,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"MAE:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: [((_metrics$mae = metrics.mae) === null || _metrics$mae === void 0 ? void 0 : _metrics$mae.toFixed(2)) || 'N/A', \" min\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 478,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"MAPE:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: [((_metrics$mape = metrics.mape) === null || _metrics$mape === void 0 ? void 0 : _metrics$mape.toFixed(1)) || 'N/A', \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 23\n                  }, this), index === 0 && /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      mt: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"success.main\",\n                      children: \"\\uD83C\\uDFC6 Highest R\\xB2 Score\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 494,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 19\n              }, this)\n            }, modelName, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Performance Ranking\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: sortedModels.map((modelName, index) => {\n            var _metrics$r2, _metrics$rmse2;\n            const metrics = modelComparison[modelName];\n            const isBestModel = modelName === bestModel;\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: index === 0 ? 'primary' : 'text.secondary',\n                  children: index + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    fontWeight: isBestModel ? 'bold' : 'normal',\n                    children: modelName.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 25\n                  }, this), isBestModel && /*#__PURE__*/_jsxDEV(Chip, {\n                    label: \"Best\",\n                    color: \"primary\",\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 23\n                }, this),\n                secondary: `R² Score: ${((_metrics$r2 = metrics.r2) === null || _metrics$r2 === void 0 ? void 0 : _metrics$r2.toFixed(3)) || 'N/A'} | RMSE: ${((_metrics$rmse2 = metrics.rmse) === null || _metrics$rmse2 === void 0 ? void 0 : _metrics$rmse2.toFixed(2)) || 'N/A'} min`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: '30%',\n                  ml: 2\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    height: 8,\n                    bgcolor: index === 0 ? 'primary.main' : 'primary.light',\n                    width: `${Math.min(100, Math.max(10, (metrics.r2 || 0) * 100))}%`,\n                    minWidth: '10%',\n                    borderRadius: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 19\n              }, this)]\n            }, modelName, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 7\n    }, this);\n  };\n  const renderFeatureImportance = () => {\n    if (!featureImportance) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Loading feature importance data...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Parse feature importance data\n    const chartData = featureImportance.chart ? JSON.parse(featureImportance.chart) : null;\n    if (!chartData) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        children: \"No feature importance data available.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 572,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Extract top features from Plotly chart data\n    let topFeatures = [];\n    if (chartData.data && chartData.data.length > 0) {\n      const plotlyData = chartData.data[0];\n\n      // Handle horizontal bar chart format (x=importance, y=feature names)\n      if (plotlyData.x && plotlyData.y) {\n        topFeatures = plotlyData.y.map((featureName, index) => ({\n          name: featureName,\n          importance: plotlyData.x[index]\n        }));\n      }\n    }\n\n    // Fallback: try to extract from other possible data structures\n    if (topFeatures.length === 0 && chartData.data) {\n      topFeatures = chartData.data;\n    }\n\n    // If still no features, show a message\n    if (topFeatures.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        children: \"No feature importance data could be extracted from the model.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [/*#__PURE__*/_jsxDEV(BubbleChart, {\n            sx: {\n              mr: 1,\n              verticalAlign: 'middle'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 13\n          }, this), \"Feature Importance\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Feature importance shows which variables have the most influence on predictions. Higher values indicate stronger influence on the model's output.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: topFeatures.slice(0, 10).map((feature, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                color: index < 3 ? 'primary' : 'action'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: `${index + 1}. ${feature.name || feature.x || `Feature ${index + 1}`}`,\n              secondary: `Importance: ${(() => {\n                const importance = feature.importance || feature.y;\n                return typeof importance === 'number' ? importance.toFixed(3) : 'N/A';\n              })()}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: '50%',\n                ml: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: 12,\n                  bgcolor: index < 3 ? 'primary.main' : 'primary.light',\n                  width: `${Math.min(100, Math.max(5, (typeof (feature.importance || feature.y) === 'number' ? feature.importance || feature.y : 0) * 100))}%`,\n                  minWidth: '5%',\n                  borderRadius: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Insights\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 652,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Info, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Top Predictors\",\n              secondary: `The top 3 features account for a significant portion of the model's predictive power.`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 661,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Info, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Feature Selection\",\n              secondary: \"Consider focusing on the top features for simplified models or data collection.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 609,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 608,\n      columnNumber: 7\n    }, this);\n  };\n  const renderPredictionScatter = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [/*#__PURE__*/_jsxDEV(Timeline, {\n            sx: {\n              mr: 1,\n              verticalAlign: 'middle'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 13\n          }, this), \"Predicted vs Actual Values\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"This chart shows how well the model's predictions match the actual values. Points closer to the diagonal line indicate more accurate predictions.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 691,\n          columnNumber: 11\n        }, this), visualizations.predictionScatter ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            mt: 2\n          },\n          children: typeof visualizations.predictionScatter === 'string' ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `data:image/png;base64,${visualizations.predictionScatter}`,\n            alt: \"Prediction Scatter Plot\",\n            style: {\n              maxWidth: '100%',\n              height: 'auto'\n            },\n            onError: e => {\n              console.error('Failed to load prediction scatter image');\n              e.target.style.display = 'none';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 17\n          }, this) : visualizations.predictionScatter.type === 'plotly' ? /*#__PURE__*/_jsxDEV(Plot, {\n            data: JSON.parse(visualizations.predictionScatter.chart).data,\n            layout: {\n              ...JSON.parse(visualizations.predictionScatter.chart).layout,\n              autosize: true,\n              height: 400\n            },\n            config: {\n              responsive: true\n            },\n            style: {\n              width: '100%',\n              height: '400px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"error\",\n            children: \"Invalid visualization format\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 699,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            color: \"text.secondary\",\n            children: activeTab === 1 ? 'Loading visualization...' : 'Click to load visualization'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 15\n          }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: () => loadVisualization('predictionScatter'),\n            sx: {\n              mt: 2\n            },\n            children: \"Load Prediction Scatter Plot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 726,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 685,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 684,\n      columnNumber: 7\n    }, this);\n  };\n  const renderResidualsPlot = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [/*#__PURE__*/_jsxDEV(Timeline, {\n            sx: {\n              mr: 1,\n              verticalAlign: 'middle'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 13\n          }, this), \"Residuals Analysis\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Residuals are the differences between predicted and actual values. Ideally, residuals should be randomly distributed around zero with no pattern.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 755,\n          columnNumber: 11\n        }, this), visualizations.residuals ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            mt: 2\n          },\n          children: typeof visualizations.residuals === 'string' ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `data:image/png;base64,${visualizations.residuals}`,\n            alt: \"Residuals Plot\",\n            style: {\n              maxWidth: '100%',\n              height: 'auto'\n            },\n            onError: e => {\n              console.error('Failed to load residuals image');\n              e.target.style.display = 'none';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 765,\n            columnNumber: 17\n          }, this) : visualizations.residuals.type === 'plotly' ? /*#__PURE__*/_jsxDEV(Plot, {\n            data: JSON.parse(visualizations.residuals.chart).data,\n            layout: {\n              ...JSON.parse(visualizations.residuals.chart).layout,\n              autosize: true,\n              height: 400\n            },\n            config: {\n              responsive: true\n            },\n            style: {\n              width: '100%',\n              height: '400px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 775,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"error\",\n            children: \"Invalid visualization format\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 763,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            color: \"text.secondary\",\n            children: activeTab === 3 ? 'Loading visualization...' : 'Click to load visualization'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 791,\n            columnNumber: 15\n          }, this), activeTab === 3 && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: () => loadVisualization('residuals'),\n            sx: {\n              mt: 2\n            },\n            children: \"Load Residuals Plot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 790,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Interpretation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 808,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Info, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 815,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 814,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Random Distribution\",\n              secondary: \"Residuals should be randomly scattered around zero with no clear pattern.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 813,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Warning, {\n                color: \"warning\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 824,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Patterns to Watch For\",\n              secondary: \"Trends, curves, or clusters in residuals indicate the model may be missing important patterns.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 827,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 823,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 749,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 748,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Model Evaluation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 840,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      paragraph: true,\n      children: \"Evaluate model performance and understand feature importance. This step helps you interpret the model's predictions and identify areas for improvement.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 843,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: activeTab,\n        onChange: handleTabChange,\n        variant: \"scrollable\",\n        scrollButtons: \"auto\",\n        indicatorColor: \"primary\",\n        textColor: \"primary\",\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(BarChart, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 22\n          }, this),\n          label: \"Best Model\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 857,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 858,\n            columnNumber: 22\n          }, this),\n          label: \"Compare Models\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 858,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(Timeline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 859,\n            columnNumber: 22\n          }, this),\n          label: \"Predictions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 859,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(BubbleChart, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 860,\n            columnNumber: 22\n          }, this),\n          label: \"Features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 860,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 861,\n            columnNumber: 22\n          }, this),\n          label: \"Residuals\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 861,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 849,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 848,\n      columnNumber: 7\n    }, this), activeTab === 0 && renderPerformanceMetrics(), activeTab === 1 && renderModelComparison(), activeTab === 2 && renderPredictionScatter(), activeTab === 3 && renderFeatureImportance(), activeTab === 4 && renderResidualsPlot(), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        onClick: onBack,\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 875,\n          columnNumber: 22\n        }, this),\n        children: \"Back\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 872,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: onNext,\n        endIcon: /*#__PURE__*/_jsxDEV(ArrowForward, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 884,\n          columnNumber: 20\n        }, this),\n        children: \"Continue to Predictions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 880,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 871,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 839,\n    columnNumber: 5\n  }, this);\n};\n_s(ModelEvaluation, \"UiOgCWU9WoYEbRup6jI/0kXtZl0=\");\n_c = ModelEvaluation;\nexport default ModelEvaluation;\nvar _c;\n$RefreshReg$(_c, \"ModelEvaluation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Plot", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Divider", "Chip", "<PERSON><PERSON>", "List", "ListItem", "ListItemIcon", "ListItemText", "Tab", "Tabs", "ArrowBack", "ArrowForward", "<PERSON><PERSON><PERSON>", "Timeline", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrendingUp", "Info", "Warning", "CheckCircle", "Error", "toast", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ModelEvaluation", "onNext", "onBack", "bestModel", "setLoading", "setError", "_s", "activeTab", "setActiveTab", "modelPerformance", "setModelPerformance", "featureImportance", "setFeatureImportance", "visualizations", "setVisualizations", "modelComparison", "setModelComparison", "selected<PERSON><PERSON>l", "setSelectedModel", "fetchModelPerformance", "fetchFeatureImportance", "fetchModelComparison", "result", "getModelPerformance", "error", "message", "getFeatureImportance", "getStatus", "model_performance", "console", "handleTabChange", "_", "newValue", "predictionScatter", "loadVisualization", "residuals", "vizType", "generateVisualization", "image", "chart", "type", "prev", "renderPerformanceMetrics", "_modelPerformance$das", "_bestModelMetrics$r", "_bestModelMetrics$rms", "_bestModelMetrics$mae", "_bestModelMetrics$map", "_bestModelMetrics$r2", "_bestModelMetrics$r3", "_bestModelMetrics$r4", "_bestModelMetrics$mae2", "_bestModelMetrics$map2", "severity", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "metrics", "dashboard", "rmse_comparison", "JSON", "parse", "getR2Color", "r2", "getR2Interpretation", "bestModelMetrics", "best_model_metrics", "rmse", "mae", "mape", "sx", "p", "mb", "variant", "gutterBottom", "mr", "verticalAlign", "container", "spacing", "item", "xs", "sm", "md", "color", "toFixed", "label", "size", "mt", "my", "primary", "secondary", "renderModelComparison", "models", "Object", "keys", "length", "sortedModels", "sort", "a", "b", "_modelComparison$a", "_modelComparison$b", "r2A", "r2B", "map", "modelName", "index", "_metrics$r", "_metrics$rmse", "_metrics$mae", "_metrics$mape", "isBestModel", "lg", "elevation", "position", "border", "borderColor", "backgroundColor", "top", "right", "zIndex", "replace", "l", "toUpperCase", "display", "justifyContent", "fontWeight", "_metrics$r2", "_metrics$rmse2", "alignItems", "gap", "width", "ml", "height", "bgcolor", "Math", "min", "max", "min<PERSON><PERSON><PERSON>", "borderRadius", "renderFeatureImportance", "chartData", "topFeatures", "data", "plotlyData", "x", "y", "featureName", "name", "importance", "slice", "feature", "renderPredictionScatter", "textAlign", "src", "alt", "style", "max<PERSON><PERSON><PERSON>", "onError", "e", "target", "layout", "autosize", "config", "responsive", "onClick", "renderResidualsPlot", "paragraph", "value", "onChange", "scrollButtons", "indicatorColor", "textColor", "icon", "startIcon", "endIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Pumptimemodel/frontend/src/components/ModelEvaluation.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport Plot from 'react-plotly.js';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Button,\r\n  Paper,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  Divider,\r\n  Chip,\r\n  Alert,\r\n  List,\r\n  ListItem,\r\n  ListItemIcon,\r\n  ListItemText,\r\n  Tab,\r\n  Tabs\r\n} from '@mui/material';\r\nimport {\r\n  ArrowBack,\r\n  ArrowForward,\r\n  BarChart,\r\n  Timeline,\r\n  BubbleChart,\r\n  TrendingUp,\r\n  Info,\r\n  Warning,\r\n  CheckCircle,\r\n  Error\r\n} from '@mui/icons-material';\r\nimport toast from 'react-hot-toast';\r\n\r\nimport { apiService } from '../services/apiService';\r\n\r\nconst ModelEvaluation = ({ onNext, onBack, bestModel, setLoading, setError }) => {\r\n  const [activeTab, setActiveTab] = useState(0);\r\n  const [modelPerformance, setModelPerformance] = useState(null);\r\n  const [featureImportance, setFeatureImportance] = useState(null);\r\n  const [visualizations, setVisualizations] = useState({});\r\n  const [modelComparison, setModelComparison] = useState(null);\r\n  const [selectedModel, setSelectedModel] = useState(null);\r\n\r\n  useEffect(() => {\r\n    fetchModelPerformance();\r\n    fetchFeatureImportance();\r\n    fetchModelComparison();\r\n  }, [bestModel]);\r\n\r\n\r\n\r\n  const fetchModelPerformance = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const result = await apiService.getModelPerformance();\r\n      setModelPerformance(result);\r\n    } catch (error) {\r\n      setError('Failed to fetch model performance: ' + error.message);\r\n      toast.error('Failed to fetch model performance');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchFeatureImportance = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const result = await apiService.getFeatureImportance();\r\n      setFeatureImportance(result);\r\n    } catch (error) {\r\n      setError('Failed to fetch feature importance: ' + error.message);\r\n      toast.error('Failed to fetch feature importance');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchModelComparison = async () => {\r\n    try {\r\n      const result = await apiService.getStatus();\r\n      if (result.model_performance) {\r\n        setModelComparison(result.model_performance);\r\n        // Set the best model as the initially selected model\r\n        if (bestModel && !selectedModel) {\r\n          setSelectedModel(bestModel);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to fetch model comparison:', error);\r\n    }\r\n  };\r\n\r\n  const handleTabChange = (_, newValue) => {\r\n    setActiveTab(newValue);\r\n\r\n    // Load visualization data for the selected tab if not already loaded\r\n    if (newValue === 2 && !visualizations.predictionScatter) {\r\n      loadVisualization('predictionScatter');\r\n    } else if (newValue === 3 && !visualizations.featureImportance) {\r\n      loadVisualization('featureImportance');\r\n    } else if (newValue === 4 && !visualizations.residuals) {\r\n      loadVisualization('residuals');\r\n    }\r\n  };\r\n\r\n  const loadVisualization = async (vizType) => {\r\n    setLoading(true);\r\n    try {\r\n      let result;\r\n\r\n      switch (vizType) {\r\n        case 'predictionScatter':\r\n          result = await apiService.generateVisualization('prediction_scatter');\r\n          // Handle both image and Plotly JSON responses\r\n          if (result.image) {\r\n            result = result.image;\r\n          } else if (result.chart) {\r\n            result = { type: 'plotly', chart: result.chart };\r\n          }\r\n          break;\r\n        case 'featureImportance':\r\n          result = await apiService.generateVisualization('feature_importance');\r\n          break;\r\n        case 'residuals':\r\n          result = await apiService.generateVisualization('residuals_plot');\r\n          // Handle both image and Plotly JSON responses\r\n          if (result.image) {\r\n            result = result.image;\r\n          } else if (result.chart) {\r\n            result = { type: 'plotly', chart: result.chart };\r\n          }\r\n          break;\r\n        default:\r\n          return;\r\n      }\r\n\r\n      setVisualizations(prev => ({\r\n        ...prev,\r\n        [vizType]: result\r\n      }));\r\n    } catch (error) {\r\n      console.error(`Failed to load ${vizType} visualization:`, error);\r\n      setError(`Failed to load ${vizType} visualization: ${error.message}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const renderPerformanceMetrics = () => {\r\n    if (!modelPerformance) {\r\n      return (\r\n        <Alert severity=\"info\">\r\n          Loading model performance metrics...\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    const metrics = modelPerformance.dashboard?.rmse_comparison ? \r\n      JSON.parse(modelPerformance.dashboard.rmse_comparison) : null;\r\n    \r\n    if (!metrics) {\r\n      return (\r\n        <Alert severity=\"warning\">\r\n          No performance metrics available for the model.\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    // Helper function to get color based on R² score\r\n    const getR2Color = (r2) => {\r\n      if (r2 >= 0.9) return 'success';\r\n      if (r2 >= 0.7) return 'primary';\r\n      if (r2 >= 0.5) return 'warning';\r\n      return 'error';\r\n    };\r\n    \r\n    // Helper function to get interpretation of R² score\r\n    const getR2Interpretation = (r2) => {\r\n      if (r2 >= 0.9) return 'Excellent fit';\r\n      if (r2 >= 0.7) return 'Good fit';\r\n      if (r2 >= 0.5) return 'Moderate fit';\r\n      if (r2 >= 0.0) return 'Poor fit';\r\n      return 'Very poor fit';\r\n    };\r\n\r\n    // Extract metrics for the best model\r\n    const bestModelMetrics = modelPerformance.best_model_metrics || {\r\n      r2: 0.7,\r\n      rmse: 45,\r\n      mae: 35,\r\n      mape: 15\r\n    };\r\n\r\n    return (\r\n      <Box>\r\n        <Paper sx={{ p: 3, mb: 3 }}>\r\n          <Typography variant=\"h6\" gutterBottom>\r\n            <BarChart sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n            Model Performance Summary\r\n          </Typography>\r\n          \r\n          <Grid container spacing={3}>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Card variant=\"outlined\">\r\n                <CardContent>\r\n                  <Typography variant=\"h6\" color=\"primary\" gutterBottom>\r\n                    {bestModelMetrics.r2?.toFixed(3) || 'N/A'}\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    R² Score\r\n                  </Typography>\r\n                  <Chip \r\n                    label={getR2Interpretation(bestModelMetrics.r2 || 0)}\r\n                    size=\"small\"\r\n                    color={getR2Color(bestModelMetrics.r2 || 0)}\r\n                    sx={{ mt: 1 }}\r\n                  />\r\n                </CardContent>\r\n              </Card>\r\n            </Grid>\r\n            \r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Card variant=\"outlined\">\r\n                <CardContent>\r\n                  <Typography variant=\"h6\" color=\"primary\" gutterBottom>\r\n                    {bestModelMetrics.rmse?.toFixed(2) || 'N/A'}\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    RMSE (minutes)\r\n                  </Typography>\r\n                  <Chip \r\n                    label=\"Root Mean Squared Error\"\r\n                    size=\"small\"\r\n                    variant=\"outlined\"\r\n                    sx={{ mt: 1 }}\r\n                  />\r\n                </CardContent>\r\n              </Card>\r\n            </Grid>\r\n            \r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Card variant=\"outlined\">\r\n                <CardContent>\r\n                  <Typography variant=\"h6\" color=\"primary\" gutterBottom>\r\n                    {bestModelMetrics.mae?.toFixed(2) || 'N/A'}\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    MAE (minutes)\r\n                  </Typography>\r\n                  <Chip \r\n                    label=\"Mean Absolute Error\"\r\n                    size=\"small\"\r\n                    variant=\"outlined\"\r\n                    sx={{ mt: 1 }}\r\n                  />\r\n                </CardContent>\r\n              </Card>\r\n            </Grid>\r\n            \r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Card variant=\"outlined\">\r\n                <CardContent>\r\n                  <Typography variant=\"h6\" color=\"primary\" gutterBottom>\r\n                    {bestModelMetrics.mape?.toFixed(1) || 'N/A'}%\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    MAPE\r\n                  </Typography>\r\n                  <Chip \r\n                    label=\"Mean Absolute Percentage Error\"\r\n                    size=\"small\"\r\n                    variant=\"outlined\"\r\n                    sx={{ mt: 1 }}\r\n                  />\r\n                </CardContent>\r\n              </Card>\r\n            </Grid>\r\n          </Grid>\r\n          \r\n          <Divider sx={{ my: 3 }} />\r\n          \r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            Model Evaluation\r\n          </Typography>\r\n          \r\n          <Alert \r\n            severity={getR2Color(bestModelMetrics.r2 || 0)}\r\n            sx={{ mb: 2 }}\r\n          >\r\n            <Typography variant=\"subtitle2\" gutterBottom>\r\n              {bestModel} - {getR2Interpretation(bestModelMetrics.r2 || 0)}\r\n            </Typography>\r\n            \r\n            <Typography variant=\"body2\">\r\n              {bestModelMetrics.r2 >= 0.7 ? (\r\n                <>\r\n                  This model shows good predictive performance. The R² score of {bestModelMetrics.r2?.toFixed(3)} \r\n                  indicates that {(bestModelMetrics.r2 * 100).toFixed(1)}% of the variance in the target variable \r\n                  is explained by the model.\r\n                </>\r\n              ) : bestModelMetrics.r2 >= 0.5 ? (\r\n                <>\r\n                  This model shows moderate predictive performance. The R² score of {bestModelMetrics.r2?.toFixed(3)} \r\n                  indicates that only {(bestModelMetrics.r2 * 100).toFixed(1)}% of the variance in the target variable \r\n                  is explained by the model. Consider adding more relevant features or collecting more data.\r\n                </>\r\n              ) : (\r\n                <>\r\n                  This model shows poor predictive performance. The R² score of {bestModelMetrics.r2?.toFixed(3)} \r\n                  indicates that only {(bestModelMetrics.r2 * 100).toFixed(1)}% of the variance in the target variable \r\n                  is explained by the model. Consider:\r\n                  <ul>\r\n                    <li>Adding more training data</li>\r\n                    <li>Engineering additional features</li>\r\n                    <li>Checking data quality</li>\r\n                    <li>Trying different algorithms</li>\r\n                  </ul>\r\n                </>\r\n              )}\r\n            </Typography>\r\n          </Alert>\r\n          \r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            Practical Interpretation\r\n          </Typography>\r\n          \r\n          <List>\r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Info color=\"primary\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary={`Average Error: ±${bestModelMetrics.mae?.toFixed(1) || 'N/A'} minutes`}\r\n                secondary=\"On average, predictions will be off by this amount\"\r\n              />\r\n            </ListItem>\r\n            \r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Info color=\"primary\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary={`Percentage Error: ${bestModelMetrics.mape?.toFixed(1) || 'N/A'}%`}\r\n                secondary=\"Relative error as a percentage of the actual value\"\r\n              />\r\n            </ListItem>\r\n          </List>\r\n        </Paper>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  const renderModelComparison = () => {\r\n    if (!modelComparison) {\r\n      return (\r\n        <Alert severity=\"info\">\r\n          Loading model comparison data...\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    const models = Object.keys(modelComparison);\r\n\r\n    if (models.length === 0) {\r\n      return (\r\n        <Alert severity=\"warning\">\r\n          No model comparison data available.\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    // Helper function to get color based on R² score\r\n    const getR2Color = (r2) => {\r\n      if (r2 >= 0.9) return 'success';\r\n      if (r2 >= 0.7) return 'primary';\r\n      if (r2 >= 0.5) return 'warning';\r\n      return 'error';\r\n    };\r\n\r\n    // Helper function to get interpretation of R² score\r\n    const getR2Interpretation = (r2) => {\r\n      if (r2 >= 0.9) return 'Excellent';\r\n      if (r2 >= 0.7) return 'Good';\r\n      if (r2 >= 0.5) return 'Moderate';\r\n      if (r2 >= 0.0) return 'Poor';\r\n      return 'Very poor';\r\n    };\r\n\r\n    // Sort models by R² score (descending)\r\n    const sortedModels = models.sort((a, b) => {\r\n      const r2A = modelComparison[a]?.r2 || 0;\r\n      const r2B = modelComparison[b]?.r2 || 0;\r\n      return r2B - r2A;\r\n    });\r\n\r\n    return (\r\n      <Box>\r\n        <Paper sx={{ p: 3, mb: 3 }}>\r\n          <Typography variant=\"h6\" gutterBottom>\r\n            <BarChart sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n            Model Comparison\r\n          </Typography>\r\n\r\n          <Alert severity=\"info\" sx={{ mb: 3 }}>\r\n            <Typography variant=\"body2\">\r\n              Compare the performance of different machine learning algorithms.\r\n              The best performing model is highlighted and used for predictions.\r\n            </Typography>\r\n          </Alert>\r\n\r\n          <Grid container spacing={2}>\r\n            {sortedModels.map((modelName, index) => {\r\n              const metrics = modelComparison[modelName];\r\n              const isBestModel = modelName === bestModel;\r\n\r\n              return (\r\n                <Grid item xs={12} md={6} lg={4} key={modelName}>\r\n                  <Card\r\n                    variant={isBestModel ? \"elevation\" : \"outlined\"}\r\n                    elevation={isBestModel ? 4 : 1}\r\n                    sx={{\r\n                      position: 'relative',\r\n                      border: isBestModel ? '2px solid' : '1px solid',\r\n                      borderColor: isBestModel ? 'primary.main' : 'divider',\r\n                      backgroundColor: isBestModel ? 'primary.50' : 'background.paper'\r\n                    }}\r\n                  >\r\n                    {isBestModel && (\r\n                      <Chip\r\n                        label=\"Best Model\"\r\n                        color=\"primary\"\r\n                        size=\"small\"\r\n                        sx={{\r\n                          position: 'absolute',\r\n                          top: 8,\r\n                          right: 8,\r\n                          zIndex: 1\r\n                        }}\r\n                      />\r\n                    )}\r\n\r\n                    <CardContent>\r\n                      <Typography variant=\"h6\" gutterBottom>\r\n                        {modelName.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\r\n                      </Typography>\r\n\r\n                      <Box sx={{ mb: 2 }}>\r\n                        <Typography variant=\"h4\" color=\"primary\" gutterBottom>\r\n                          {metrics.r2?.toFixed(3) || 'N/A'}\r\n                        </Typography>\r\n                        <Typography variant=\"body2\" color=\"text.secondary\">\r\n                          R² Score\r\n                        </Typography>\r\n                        <Chip\r\n                          label={getR2Interpretation(metrics.r2 || 0)}\r\n                          size=\"small\"\r\n                          color={getR2Color(metrics.r2 || 0)}\r\n                          sx={{ mt: 1 }}\r\n                        />\r\n                      </Box>\r\n\r\n                      <Divider sx={{ my: 2 }} />\r\n\r\n                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\r\n                        <Typography variant=\"body2\" color=\"text.secondary\">\r\n                          RMSE:\r\n                        </Typography>\r\n                        <Typography variant=\"body2\" fontWeight=\"medium\">\r\n                          {metrics.rmse?.toFixed(2) || 'N/A'} min\r\n                        </Typography>\r\n                      </Box>\r\n\r\n                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\r\n                        <Typography variant=\"body2\" color=\"text.secondary\">\r\n                          MAE:\r\n                        </Typography>\r\n                        <Typography variant=\"body2\" fontWeight=\"medium\">\r\n                          {metrics.mae?.toFixed(2) || 'N/A'} min\r\n                        </Typography>\r\n                      </Box>\r\n\r\n                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\r\n                        <Typography variant=\"body2\" color=\"text.secondary\">\r\n                          MAPE:\r\n                        </Typography>\r\n                        <Typography variant=\"body2\" fontWeight=\"medium\">\r\n                          {metrics.mape?.toFixed(1) || 'N/A'}%\r\n                        </Typography>\r\n                      </Box>\r\n\r\n                      {index === 0 && (\r\n                        <Box sx={{ mt: 2 }}>\r\n                          <Typography variant=\"caption\" color=\"success.main\">\r\n                            🏆 Highest R² Score\r\n                          </Typography>\r\n                        </Box>\r\n                      )}\r\n                    </CardContent>\r\n                  </Card>\r\n                </Grid>\r\n              );\r\n            })}\r\n          </Grid>\r\n\r\n          <Divider sx={{ my: 3 }} />\r\n\r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            Performance Ranking\r\n          </Typography>\r\n\r\n          <List>\r\n            {sortedModels.map((modelName, index) => {\r\n              const metrics = modelComparison[modelName];\r\n              const isBestModel = modelName === bestModel;\r\n\r\n              return (\r\n                <ListItem key={modelName}>\r\n                  <ListItemIcon>\r\n                    <Typography variant=\"h6\" color={index === 0 ? 'primary' : 'text.secondary'}>\r\n                      {index + 1}\r\n                    </Typography>\r\n                  </ListItemIcon>\r\n                  <ListItemText\r\n                    primary={\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                        <Typography variant=\"subtitle1\" fontWeight={isBestModel ? 'bold' : 'normal'}>\r\n                          {modelName.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\r\n                        </Typography>\r\n                        {isBestModel && (\r\n                          <Chip label=\"Best\" color=\"primary\" size=\"small\" />\r\n                        )}\r\n                      </Box>\r\n                    }\r\n                    secondary={`R² Score: ${metrics.r2?.toFixed(3) || 'N/A'} | RMSE: ${metrics.rmse?.toFixed(2) || 'N/A'} min`}\r\n                  />\r\n                  <Box sx={{ width: '30%', ml: 2 }}>\r\n                    <Box\r\n                      sx={{\r\n                        height: 8,\r\n                        bgcolor: index === 0 ? 'primary.main' : 'primary.light',\r\n                        width: `${Math.min(100, Math.max(10, (metrics.r2 || 0) * 100))}%`,\r\n                        minWidth: '10%',\r\n                        borderRadius: 1\r\n                      }}\r\n                    />\r\n                  </Box>\r\n                </ListItem>\r\n              );\r\n            })}\r\n          </List>\r\n        </Paper>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  const renderFeatureImportance = () => {\r\n    if (!featureImportance) {\r\n      return (\r\n        <Alert severity=\"info\">\r\n          Loading feature importance data...\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    // Parse feature importance data\r\n    const chartData = featureImportance.chart ?\r\n      JSON.parse(featureImportance.chart) : null;\r\n\r\n    if (!chartData) {\r\n      return (\r\n        <Alert severity=\"warning\">\r\n          No feature importance data available.\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    // Extract top features from Plotly chart data\r\n    let topFeatures = [];\r\n\r\n    if (chartData.data && chartData.data.length > 0) {\r\n      const plotlyData = chartData.data[0];\r\n\r\n      // Handle horizontal bar chart format (x=importance, y=feature names)\r\n      if (plotlyData.x && plotlyData.y) {\r\n        topFeatures = plotlyData.y.map((featureName, index) => ({\r\n          name: featureName,\r\n          importance: plotlyData.x[index]\r\n        }));\r\n      }\r\n    }\r\n\r\n    // Fallback: try to extract from other possible data structures\r\n    if (topFeatures.length === 0 && chartData.data) {\r\n      topFeatures = chartData.data;\r\n    }\r\n\r\n    // If still no features, show a message\r\n    if (topFeatures.length === 0) {\r\n      return (\r\n        <Alert severity=\"warning\">\r\n          No feature importance data could be extracted from the model.\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box>\r\n        <Paper sx={{ p: 3, mb: 3 }}>\r\n          <Typography variant=\"h6\" gutterBottom>\r\n            <BubbleChart sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n            Feature Importance\r\n          </Typography>\r\n\r\n          <Alert severity=\"info\" sx={{ mb: 3 }}>\r\n            <Typography variant=\"body2\">\r\n              Feature importance shows which variables have the most influence on predictions.\r\n              Higher values indicate stronger influence on the model's output.\r\n            </Typography>\r\n          </Alert>\r\n\r\n          <List>\r\n            {topFeatures.slice(0, 10).map((feature, index) => (\r\n              <ListItem key={index}>\r\n                <ListItemIcon>\r\n                  <TrendingUp color={index < 3 ? 'primary' : 'action'} />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary={`${index + 1}. ${feature.name || feature.x || `Feature ${index + 1}`}`}\r\n                  secondary={`Importance: ${(() => {\r\n                    const importance = feature.importance || feature.y;\r\n                    return typeof importance === 'number' ? importance.toFixed(3) : 'N/A';\r\n                  })()}`}\r\n                />\r\n                <Box sx={{ width: '50%', ml: 2 }}>\r\n                  <Box\r\n                    sx={{\r\n                      height: 12,\r\n                      bgcolor: index < 3 ? 'primary.main' : 'primary.light',\r\n                      width: `${Math.min(100, Math.max(5, (typeof (feature.importance || feature.y) === 'number' ? (feature.importance || feature.y) : 0) * 100))}%`,\r\n                      minWidth: '5%',\r\n                      borderRadius: 1\r\n                    }}\r\n                  />\r\n                </Box>\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n          \r\n          <Divider sx={{ my: 3 }} />\r\n          \r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            Insights\r\n          </Typography>\r\n          \r\n          <List>\r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Info color=\"primary\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary=\"Top Predictors\"\r\n                secondary={`The top 3 features account for a significant portion of the model's predictive power.`}\r\n              />\r\n            </ListItem>\r\n            \r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Info color=\"primary\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary=\"Feature Selection\"\r\n                secondary=\"Consider focusing on the top features for simplified models or data collection.\"\r\n              />\r\n            </ListItem>\r\n          </List>\r\n        </Paper>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  const renderPredictionScatter = () => {\r\n    return (\r\n      <Box>\r\n        <Paper sx={{ p: 3, mb: 3 }}>\r\n          <Typography variant=\"h6\" gutterBottom>\r\n            <Timeline sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n            Predicted vs Actual Values\r\n          </Typography>\r\n          \r\n          <Alert severity=\"info\" sx={{ mb: 3 }}>\r\n            <Typography variant=\"body2\">\r\n              This chart shows how well the model's predictions match the actual values.\r\n              Points closer to the diagonal line indicate more accurate predictions.\r\n            </Typography>\r\n          </Alert>\r\n          \r\n          {visualizations.predictionScatter ? (\r\n            <Box sx={{ textAlign: 'center', mt: 2 }}>\r\n              {typeof visualizations.predictionScatter === 'string' ? (\r\n                <img\r\n                  src={`data:image/png;base64,${visualizations.predictionScatter}`}\r\n                  alt=\"Prediction Scatter Plot\"\r\n                  style={{ maxWidth: '100%', height: 'auto' }}\r\n                  onError={(e) => {\r\n                    console.error('Failed to load prediction scatter image');\r\n                    e.target.style.display = 'none';\r\n                  }}\r\n                />\r\n              ) : visualizations.predictionScatter.type === 'plotly' ? (\r\n                <Plot\r\n                  data={JSON.parse(visualizations.predictionScatter.chart).data}\r\n                  layout={{\r\n                    ...JSON.parse(visualizations.predictionScatter.chart).layout,\r\n                    autosize: true,\r\n                    height: 400\r\n                  }}\r\n                  config={{ responsive: true }}\r\n                  style={{ width: '100%', height: '400px' }}\r\n                />\r\n              ) : (\r\n                <Typography color=\"error\">Invalid visualization format</Typography>\r\n              )}\r\n            </Box>\r\n          ) : (\r\n            <Box sx={{ textAlign: 'center', p: 4 }}>\r\n              <Typography color=\"text.secondary\">\r\n                {activeTab === 1 ? 'Loading visualization...' : 'Click to load visualization'}\r\n              </Typography>\r\n              {activeTab === 1 && (\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  onClick={() => loadVisualization('predictionScatter')}\r\n                  sx={{ mt: 2 }}\r\n                >\r\n                  Load Prediction Scatter Plot\r\n                </Button>\r\n              )}\r\n            </Box>\r\n          )}\r\n        </Paper>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  const renderResidualsPlot = () => {\r\n    return (\r\n      <Box>\r\n        <Paper sx={{ p: 3, mb: 3 }}>\r\n          <Typography variant=\"h6\" gutterBottom>\r\n            <Timeline sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n            Residuals Analysis\r\n          </Typography>\r\n          \r\n          <Alert severity=\"info\" sx={{ mb: 3 }}>\r\n            <Typography variant=\"body2\">\r\n              Residuals are the differences between predicted and actual values.\r\n              Ideally, residuals should be randomly distributed around zero with no pattern.\r\n            </Typography>\r\n          </Alert>\r\n          \r\n          {visualizations.residuals ? (\r\n            <Box sx={{ textAlign: 'center', mt: 2 }}>\r\n              {typeof visualizations.residuals === 'string' ? (\r\n                <img\r\n                  src={`data:image/png;base64,${visualizations.residuals}`}\r\n                  alt=\"Residuals Plot\"\r\n                  style={{ maxWidth: '100%', height: 'auto' }}\r\n                  onError={(e) => {\r\n                    console.error('Failed to load residuals image');\r\n                    e.target.style.display = 'none';\r\n                  }}\r\n                />\r\n              ) : visualizations.residuals.type === 'plotly' ? (\r\n                <Plot\r\n                  data={JSON.parse(visualizations.residuals.chart).data}\r\n                  layout={{\r\n                    ...JSON.parse(visualizations.residuals.chart).layout,\r\n                    autosize: true,\r\n                    height: 400\r\n                  }}\r\n                  config={{ responsive: true }}\r\n                  style={{ width: '100%', height: '400px' }}\r\n                />\r\n              ) : (\r\n                <Typography color=\"error\">Invalid visualization format</Typography>\r\n              )}\r\n            </Box>\r\n          ) : (\r\n            <Box sx={{ textAlign: 'center', p: 4 }}>\r\n              <Typography color=\"text.secondary\">\r\n                {activeTab === 3 ? 'Loading visualization...' : 'Click to load visualization'}\r\n              </Typography>\r\n              {activeTab === 3 && (\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  onClick={() => loadVisualization('residuals')}\r\n                  sx={{ mt: 2 }}\r\n                >\r\n                  Load Residuals Plot\r\n                </Button>\r\n              )}\r\n            </Box>\r\n          )}\r\n          \r\n          <Divider sx={{ my: 3 }} />\r\n          \r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            Interpretation\r\n          </Typography>\r\n          \r\n          <List>\r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Info color=\"primary\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary=\"Random Distribution\"\r\n                secondary=\"Residuals should be randomly scattered around zero with no clear pattern.\"\r\n              />\r\n            </ListItem>\r\n            \r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Warning color=\"warning\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary=\"Patterns to Watch For\"\r\n                secondary=\"Trends, curves, or clusters in residuals indicate the model may be missing important patterns.\"\r\n              />\r\n            </ListItem>\r\n          </List>\r\n        </Paper>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Box>\r\n      <Typography variant=\"h4\" gutterBottom>\r\n        Model Evaluation\r\n      </Typography>\r\n      <Typography variant=\"body1\" color=\"text.secondary\" paragraph>\r\n        Evaluate model performance and understand feature importance.\r\n        This step helps you interpret the model's predictions and identify areas for improvement.\r\n      </Typography>\r\n      \r\n      <Paper sx={{ mb: 3 }}>\r\n        <Tabs\r\n          value={activeTab}\r\n          onChange={handleTabChange}\r\n          variant=\"scrollable\"\r\n          scrollButtons=\"auto\"\r\n          indicatorColor=\"primary\"\r\n          textColor=\"primary\"\r\n        >\r\n          <Tab icon={<BarChart />} label=\"Best Model\" />\r\n          <Tab icon={<TrendingUp />} label=\"Compare Models\" />\r\n          <Tab icon={<Timeline />} label=\"Predictions\" />\r\n          <Tab icon={<BubbleChart />} label=\"Features\" />\r\n          <Tab icon={<TrendingUp />} label=\"Residuals\" />\r\n        </Tabs>\r\n      </Paper>\r\n      \r\n      {activeTab === 0 && renderPerformanceMetrics()}\r\n      {activeTab === 1 && renderModelComparison()}\r\n      {activeTab === 2 && renderPredictionScatter()}\r\n      {activeTab === 3 && renderFeatureImportance()}\r\n      {activeTab === 4 && renderResidualsPlot()}\r\n      \r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>\r\n        <Button \r\n          variant=\"outlined\" \r\n          onClick={onBack}\r\n          startIcon={<ArrowBack />}\r\n        >\r\n          Back\r\n        </Button>\r\n        \r\n        <Button \r\n          variant=\"contained\" \r\n          color=\"primary\" \r\n          onClick={onNext}\r\n          endIcon={<ArrowForward />}\r\n        >\r\n          Continue to Predictions\r\n        </Button>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ModelEvaluation;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,GAAG,EACHC,IAAI,QACC,eAAe;AACtB,SACEC,SAAS,EACTC,YAAY,EACZC,QAAQ,EACRC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,WAAW,EACXC,KAAK,QACA,qBAAqB;AAC5B,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,UAAU,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,eAAe,GAAGA,CAAC;EAAEC,MAAM;EAAEC,MAAM;EAAEC,SAAS;EAAEC,UAAU;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC4C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC8C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAM,CAACkD,eAAe,EAAEC,kBAAkB,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAExDC,SAAS,CAAC,MAAM;IACdqD,qBAAqB,CAAC,CAAC;IACvBC,sBAAsB,CAAC,CAAC;IACxBC,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAAClB,SAAS,CAAC,CAAC;EAIf,MAAMgB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxCf,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMkB,MAAM,GAAG,MAAM3B,UAAU,CAAC4B,mBAAmB,CAAC,CAAC;MACrDb,mBAAmB,CAACY,MAAM,CAAC;IAC7B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdnB,QAAQ,CAAC,qCAAqC,GAAGmB,KAAK,CAACC,OAAO,CAAC;MAC/D/B,KAAK,CAAC8B,KAAK,CAAC,mCAAmC,CAAC;IAClD,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgB,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzChB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMkB,MAAM,GAAG,MAAM3B,UAAU,CAAC+B,oBAAoB,CAAC,CAAC;MACtDd,oBAAoB,CAACU,MAAM,CAAC;IAC9B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdnB,QAAQ,CAAC,sCAAsC,GAAGmB,KAAK,CAACC,OAAO,CAAC;MAChE/B,KAAK,CAAC8B,KAAK,CAAC,oCAAoC,CAAC;IACnD,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM3B,UAAU,CAACgC,SAAS,CAAC,CAAC;MAC3C,IAAIL,MAAM,CAACM,iBAAiB,EAAE;QAC5BZ,kBAAkB,CAACM,MAAM,CAACM,iBAAiB,CAAC;QAC5C;QACA,IAAIzB,SAAS,IAAI,CAACc,aAAa,EAAE;UAC/BC,gBAAgB,CAACf,SAAS,CAAC;QAC7B;MACF;IACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;EACF,CAAC;EAED,MAAMM,eAAe,GAAGA,CAACC,CAAC,EAAEC,QAAQ,KAAK;IACvCxB,YAAY,CAACwB,QAAQ,CAAC;;IAEtB;IACA,IAAIA,QAAQ,KAAK,CAAC,IAAI,CAACnB,cAAc,CAACoB,iBAAiB,EAAE;MACvDC,iBAAiB,CAAC,mBAAmB,CAAC;IACxC,CAAC,MAAM,IAAIF,QAAQ,KAAK,CAAC,IAAI,CAACnB,cAAc,CAACF,iBAAiB,EAAE;MAC9DuB,iBAAiB,CAAC,mBAAmB,CAAC;IACxC,CAAC,MAAM,IAAIF,QAAQ,KAAK,CAAC,IAAI,CAACnB,cAAc,CAACsB,SAAS,EAAE;MACtDD,iBAAiB,CAAC,WAAW,CAAC;IAChC;EACF,CAAC;EAED,MAAMA,iBAAiB,GAAG,MAAOE,OAAO,IAAK;IAC3ChC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAIkB,MAAM;MAEV,QAAQc,OAAO;QACb,KAAK,mBAAmB;UACtBd,MAAM,GAAG,MAAM3B,UAAU,CAAC0C,qBAAqB,CAAC,oBAAoB,CAAC;UACrE;UACA,IAAIf,MAAM,CAACgB,KAAK,EAAE;YAChBhB,MAAM,GAAGA,MAAM,CAACgB,KAAK;UACvB,CAAC,MAAM,IAAIhB,MAAM,CAACiB,KAAK,EAAE;YACvBjB,MAAM,GAAG;cAAEkB,IAAI,EAAE,QAAQ;cAAED,KAAK,EAAEjB,MAAM,CAACiB;YAAM,CAAC;UAClD;UACA;QACF,KAAK,mBAAmB;UACtBjB,MAAM,GAAG,MAAM3B,UAAU,CAAC0C,qBAAqB,CAAC,oBAAoB,CAAC;UACrE;QACF,KAAK,WAAW;UACdf,MAAM,GAAG,MAAM3B,UAAU,CAAC0C,qBAAqB,CAAC,gBAAgB,CAAC;UACjE;UACA,IAAIf,MAAM,CAACgB,KAAK,EAAE;YAChBhB,MAAM,GAAGA,MAAM,CAACgB,KAAK;UACvB,CAAC,MAAM,IAAIhB,MAAM,CAACiB,KAAK,EAAE;YACvBjB,MAAM,GAAG;cAAEkB,IAAI,EAAE,QAAQ;cAAED,KAAK,EAAEjB,MAAM,CAACiB;YAAM,CAAC;UAClD;UACA;QACF;UACE;MACJ;MAEAzB,iBAAiB,CAAC2B,IAAI,KAAK;QACzB,GAAGA,IAAI;QACP,CAACL,OAAO,GAAGd;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,kBAAkBY,OAAO,iBAAiB,EAAEZ,KAAK,CAAC;MAChEnB,QAAQ,CAAC,kBAAkB+B,OAAO,mBAAmBZ,KAAK,CAACC,OAAO,EAAE,CAAC;IACvE,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,wBAAwB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACrC,IAAI,CAAC3C,gBAAgB,EAAE;MACrB,oBACEZ,OAAA,CAACpB,KAAK;QAAC4E,QAAQ,EAAC,MAAM;QAAAC,QAAA,EAAC;MAEvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IAEA,MAAMC,OAAO,GAAG,CAAAhB,qBAAA,GAAAlC,gBAAgB,CAACmD,SAAS,cAAAjB,qBAAA,eAA1BA,qBAAA,CAA4BkB,eAAe,GACzDC,IAAI,CAACC,KAAK,CAACtD,gBAAgB,CAACmD,SAAS,CAACC,eAAe,CAAC,GAAG,IAAI;IAE/D,IAAI,CAACF,OAAO,EAAE;MACZ,oBACE9D,OAAA,CAACpB,KAAK;QAAC4E,QAAQ,EAAC,SAAS;QAAAC,QAAA,EAAC;MAE1B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;;IAEA;IACA,MAAMM,UAAU,GAAIC,EAAE,IAAK;MACzB,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,SAAS;MAC/B,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,SAAS;MAC/B,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,SAAS;MAC/B,OAAO,OAAO;IAChB,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAID,EAAE,IAAK;MAClC,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,eAAe;MACrC,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,UAAU;MAChC,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,cAAc;MACpC,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,UAAU;MAChC,OAAO,eAAe;IACxB,CAAC;;IAED;IACA,MAAME,gBAAgB,GAAG1D,gBAAgB,CAAC2D,kBAAkB,IAAI;MAC9DH,EAAE,EAAE,GAAG;MACPI,IAAI,EAAE,EAAE;MACRC,GAAG,EAAE,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IAED,oBACE1E,OAAA,CAAC7B,GAAG;MAAAsF,QAAA,eACFzD,OAAA,CAAC1B,KAAK;QAACqG,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,gBACzBzD,OAAA,CAAC5B,UAAU;UAAC0G,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAtB,QAAA,gBACnCzD,OAAA,CAACX,QAAQ;YAACsF,EAAE,EAAE;cAAEK,EAAE,EAAE,CAAC;cAAEC,aAAa,EAAE;YAAS;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BAEtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7D,OAAA,CAACzB,IAAI;UAAC2G,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA1B,QAAA,gBACzBzD,OAAA,CAACzB,IAAI;YAAC6G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9BzD,OAAA,CAACxB,IAAI;cAACsG,OAAO,EAAC,UAAU;cAAArB,QAAA,eACtBzD,OAAA,CAACvB,WAAW;gBAAAgF,QAAA,gBACVzD,OAAA,CAAC5B,UAAU;kBAAC0G,OAAO,EAAC,IAAI;kBAACU,KAAK,EAAC,SAAS;kBAACT,YAAY;kBAAAtB,QAAA,EAClD,EAAAV,mBAAA,GAAAuB,gBAAgB,CAACF,EAAE,cAAArB,mBAAA,uBAAnBA,mBAAA,CAAqB0C,OAAO,CAAC,CAAC,CAAC,KAAI;gBAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACb7D,OAAA,CAAC5B,UAAU;kBAAC0G,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAA/B,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7D,OAAA,CAACrB,IAAI;kBACH+G,KAAK,EAAErB,mBAAmB,CAACC,gBAAgB,CAACF,EAAE,IAAI,CAAC,CAAE;kBACrDuB,IAAI,EAAC,OAAO;kBACZH,KAAK,EAAErB,UAAU,CAACG,gBAAgB,CAACF,EAAE,IAAI,CAAC,CAAE;kBAC5CO,EAAE,EAAE;oBAAEiB,EAAE,EAAE;kBAAE;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP7D,OAAA,CAACzB,IAAI;YAAC6G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9BzD,OAAA,CAACxB,IAAI;cAACsG,OAAO,EAAC,UAAU;cAAArB,QAAA,eACtBzD,OAAA,CAACvB,WAAW;gBAAAgF,QAAA,gBACVzD,OAAA,CAAC5B,UAAU;kBAAC0G,OAAO,EAAC,IAAI;kBAACU,KAAK,EAAC,SAAS;kBAACT,YAAY;kBAAAtB,QAAA,EAClD,EAAAT,qBAAA,GAAAsB,gBAAgB,CAACE,IAAI,cAAAxB,qBAAA,uBAArBA,qBAAA,CAAuByC,OAAO,CAAC,CAAC,CAAC,KAAI;gBAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACb7D,OAAA,CAAC5B,UAAU;kBAAC0G,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAA/B,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7D,OAAA,CAACrB,IAAI;kBACH+G,KAAK,EAAC,yBAAyB;kBAC/BC,IAAI,EAAC,OAAO;kBACZb,OAAO,EAAC,UAAU;kBAClBH,EAAE,EAAE;oBAAEiB,EAAE,EAAE;kBAAE;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP7D,OAAA,CAACzB,IAAI;YAAC6G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9BzD,OAAA,CAACxB,IAAI;cAACsG,OAAO,EAAC,UAAU;cAAArB,QAAA,eACtBzD,OAAA,CAACvB,WAAW;gBAAAgF,QAAA,gBACVzD,OAAA,CAAC5B,UAAU;kBAAC0G,OAAO,EAAC,IAAI;kBAACU,KAAK,EAAC,SAAS;kBAACT,YAAY;kBAAAtB,QAAA,EAClD,EAAAR,qBAAA,GAAAqB,gBAAgB,CAACG,GAAG,cAAAxB,qBAAA,uBAApBA,qBAAA,CAAsBwC,OAAO,CAAC,CAAC,CAAC,KAAI;gBAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACb7D,OAAA,CAAC5B,UAAU;kBAAC0G,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAA/B,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7D,OAAA,CAACrB,IAAI;kBACH+G,KAAK,EAAC,qBAAqB;kBAC3BC,IAAI,EAAC,OAAO;kBACZb,OAAO,EAAC,UAAU;kBAClBH,EAAE,EAAE;oBAAEiB,EAAE,EAAE;kBAAE;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP7D,OAAA,CAACzB,IAAI;YAAC6G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9BzD,OAAA,CAACxB,IAAI;cAACsG,OAAO,EAAC,UAAU;cAAArB,QAAA,eACtBzD,OAAA,CAACvB,WAAW;gBAAAgF,QAAA,gBACVzD,OAAA,CAAC5B,UAAU;kBAAC0G,OAAO,EAAC,IAAI;kBAACU,KAAK,EAAC,SAAS;kBAACT,YAAY;kBAAAtB,QAAA,GAClD,EAAAP,qBAAA,GAAAoB,gBAAgB,CAACI,IAAI,cAAAxB,qBAAA,uBAArBA,qBAAA,CAAuBuC,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,GAC9C;gBAAA;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7D,OAAA,CAAC5B,UAAU;kBAAC0G,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAA/B,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7D,OAAA,CAACrB,IAAI;kBACH+G,KAAK,EAAC,gCAAgC;kBACtCC,IAAI,EAAC,OAAO;kBACZb,OAAO,EAAC,UAAU;kBAClBH,EAAE,EAAE;oBAAEiB,EAAE,EAAE;kBAAE;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEP7D,OAAA,CAACtB,OAAO;UAACiG,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE;QAAE;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE1B7D,OAAA,CAAC5B,UAAU;UAAC0G,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAtB,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7D,OAAA,CAACpB,KAAK;UACJ4E,QAAQ,EAAEW,UAAU,CAACG,gBAAgB,CAACF,EAAE,IAAI,CAAC,CAAE;UAC/CO,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAApB,QAAA,gBAEdzD,OAAA,CAAC5B,UAAU;YAAC0G,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAtB,QAAA,GACzCnD,SAAS,EAAC,KAAG,EAAC+D,mBAAmB,CAACC,gBAAgB,CAACF,EAAE,IAAI,CAAC,CAAC;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eAEb7D,OAAA,CAAC5B,UAAU;YAAC0G,OAAO,EAAC,OAAO;YAAArB,QAAA,EACxBa,gBAAgB,CAACF,EAAE,IAAI,GAAG,gBACzBpE,OAAA,CAAAE,SAAA;cAAAuD,QAAA,GAAE,mEAC8D,GAAAN,oBAAA,GAACmB,gBAAgB,CAACF,EAAE,cAAAjB,oBAAA,uBAAnBA,oBAAA,CAAqBsC,OAAO,CAAC,CAAC,CAAC,EAAC,iBAChF,EAAC,CAACnB,gBAAgB,CAACF,EAAE,GAAG,GAAG,EAAEqB,OAAO,CAAC,CAAC,CAAC,EAAC,qEAEzD;YAAA,eAAE,CAAC,GACDnB,gBAAgB,CAACF,EAAE,IAAI,GAAG,gBAC5BpE,OAAA,CAAAE,SAAA;cAAAuD,QAAA,GAAE,uEACkE,GAAAL,oBAAA,GAACkB,gBAAgB,CAACF,EAAE,cAAAhB,oBAAA,uBAAnBA,oBAAA,CAAqBqC,OAAO,CAAC,CAAC,CAAC,EAAC,sBAC/E,EAAC,CAACnB,gBAAgB,CAACF,EAAE,GAAG,GAAG,EAAEqB,OAAO,CAAC,CAAC,CAAC,EAAC,qIAE9D;YAAA,eAAE,CAAC,gBAEHzF,OAAA,CAAAE,SAAA;cAAAuD,QAAA,GAAE,mEAC8D,GAAAJ,oBAAA,GAACiB,gBAAgB,CAACF,EAAE,cAAAf,oBAAA,uBAAnBA,oBAAA,CAAqBoC,OAAO,CAAC,CAAC,CAAC,EAAC,sBAC3E,EAAC,CAACnB,gBAAgB,CAACF,EAAE,GAAG,GAAG,EAAEqB,OAAO,CAAC,CAAC,CAAC,EAAC,+EAE5D,eAAAzF,OAAA;gBAAAyD,QAAA,gBACEzD,OAAA;kBAAAyD,QAAA,EAAI;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClC7D,OAAA;kBAAAyD,QAAA,EAAI;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxC7D,OAAA;kBAAAyD,QAAA,EAAI;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9B7D,OAAA;kBAAAyD,QAAA,EAAI;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA,eACL;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAER7D,OAAA,CAAC5B,UAAU;UAAC0G,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAtB,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7D,OAAA,CAACnB,IAAI;UAAA4E,QAAA,gBACHzD,OAAA,CAAClB,QAAQ;YAAA2E,QAAA,gBACPzD,OAAA,CAACjB,YAAY;cAAA0E,QAAA,eACXzD,OAAA,CAACP,IAAI;gBAAC+F,KAAK,EAAC;cAAS;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACf7D,OAAA,CAAChB,YAAY;cACX8G,OAAO,EAAE,mBAAmB,EAAAxC,sBAAA,GAAAgB,gBAAgB,CAACG,GAAG,cAAAnB,sBAAA,uBAApBA,sBAAA,CAAsBmC,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,UAAW;cAChFM,SAAS,EAAC;YAAoD;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAEX7D,OAAA,CAAClB,QAAQ;YAAA2E,QAAA,gBACPzD,OAAA,CAACjB,YAAY;cAAA0E,QAAA,eACXzD,OAAA,CAACP,IAAI;gBAAC+F,KAAK,EAAC;cAAS;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACf7D,OAAA,CAAChB,YAAY;cACX8G,OAAO,EAAE,qBAAqB,EAAAvC,sBAAA,GAAAe,gBAAgB,CAACI,IAAI,cAAAnB,sBAAA,uBAArBA,sBAAA,CAAuBkC,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,GAAI;cAC5EM,SAAS,EAAC;YAAoD;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAED,MAAMmC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAAC9E,eAAe,EAAE;MACpB,oBACElB,OAAA,CAACpB,KAAK;QAAC4E,QAAQ,EAAC,MAAM;QAAAC,QAAA,EAAC;MAEvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IAEA,MAAMoC,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACjF,eAAe,CAAC;IAE3C,IAAI+E,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;MACvB,oBACEpG,OAAA,CAACpB,KAAK;QAAC4E,QAAQ,EAAC,SAAS;QAAAC,QAAA,EAAC;MAE1B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;;IAEA;IACA,MAAMM,UAAU,GAAIC,EAAE,IAAK;MACzB,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,SAAS;MAC/B,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,SAAS;MAC/B,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,SAAS;MAC/B,OAAO,OAAO;IAChB,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAID,EAAE,IAAK;MAClC,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,WAAW;MACjC,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,MAAM;MAC5B,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,UAAU;MAChC,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,MAAM;MAC5B,OAAO,WAAW;IACpB,CAAC;;IAED;IACA,MAAMiC,YAAY,GAAGJ,MAAM,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAAA,IAAAC,kBAAA,EAAAC,kBAAA;MACzC,MAAMC,GAAG,GAAG,EAAAF,kBAAA,GAAAvF,eAAe,CAACqF,CAAC,CAAC,cAAAE,kBAAA,uBAAlBA,kBAAA,CAAoBrC,EAAE,KAAI,CAAC;MACvC,MAAMwC,GAAG,GAAG,EAAAF,kBAAA,GAAAxF,eAAe,CAACsF,CAAC,CAAC,cAAAE,kBAAA,uBAAlBA,kBAAA,CAAoBtC,EAAE,KAAI,CAAC;MACvC,OAAOwC,GAAG,GAAGD,GAAG;IAClB,CAAC,CAAC;IAEF,oBACE3G,OAAA,CAAC7B,GAAG;MAAAsF,QAAA,eACFzD,OAAA,CAAC1B,KAAK;QAACqG,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,gBACzBzD,OAAA,CAAC5B,UAAU;UAAC0G,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAtB,QAAA,gBACnCzD,OAAA,CAACX,QAAQ;YAACsF,EAAE,EAAE;cAAEK,EAAE,EAAE,CAAC;cAAEC,aAAa,EAAE;YAAS;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAEtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7D,OAAA,CAACpB,KAAK;UAAC4E,QAAQ,EAAC,MAAM;UAACmB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAApB,QAAA,eACnCzD,OAAA,CAAC5B,UAAU;YAAC0G,OAAO,EAAC,OAAO;YAAArB,QAAA,EAAC;UAG5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAER7D,OAAA,CAACzB,IAAI;UAAC2G,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA1B,QAAA,EACxB4C,YAAY,CAACQ,GAAG,CAAC,CAACC,SAAS,EAAEC,KAAK,KAAK;YAAA,IAAAC,UAAA,EAAAC,aAAA,EAAAC,YAAA,EAAAC,aAAA;YACtC,MAAMrD,OAAO,GAAG5C,eAAe,CAAC4F,SAAS,CAAC;YAC1C,MAAMM,WAAW,GAAGN,SAAS,KAAKxG,SAAS;YAE3C,oBACEN,OAAA,CAACzB,IAAI;cAAC6G,IAAI;cAACC,EAAE,EAAE,EAAG;cAACE,EAAE,EAAE,CAAE;cAAC8B,EAAE,EAAE,CAAE;cAAA5D,QAAA,eAC9BzD,OAAA,CAACxB,IAAI;gBACHsG,OAAO,EAAEsC,WAAW,GAAG,WAAW,GAAG,UAAW;gBAChDE,SAAS,EAAEF,WAAW,GAAG,CAAC,GAAG,CAAE;gBAC/BzC,EAAE,EAAE;kBACF4C,QAAQ,EAAE,UAAU;kBACpBC,MAAM,EAAEJ,WAAW,GAAG,WAAW,GAAG,WAAW;kBAC/CK,WAAW,EAAEL,WAAW,GAAG,cAAc,GAAG,SAAS;kBACrDM,eAAe,EAAEN,WAAW,GAAG,YAAY,GAAG;gBAChD,CAAE;gBAAA3D,QAAA,GAED2D,WAAW,iBACVpH,OAAA,CAACrB,IAAI;kBACH+G,KAAK,EAAC,YAAY;kBAClBF,KAAK,EAAC,SAAS;kBACfG,IAAI,EAAC,OAAO;kBACZhB,EAAE,EAAE;oBACF4C,QAAQ,EAAE,UAAU;oBACpBI,GAAG,EAAE,CAAC;oBACNC,KAAK,EAAE,CAAC;oBACRC,MAAM,EAAE;kBACV;gBAAE;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACF,eAED7D,OAAA,CAACvB,WAAW;kBAAAgF,QAAA,gBACVzD,OAAA,CAAC5B,UAAU;oBAAC0G,OAAO,EAAC,IAAI;oBAACC,YAAY;oBAAAtB,QAAA,EAClCqD,SAAS,CAACgB,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC;kBAAC;oBAAAtE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eAEb7D,OAAA,CAAC7B,GAAG;oBAACwG,EAAE,EAAE;sBAAEE,EAAE,EAAE;oBAAE,CAAE;oBAAApB,QAAA,gBACjBzD,OAAA,CAAC5B,UAAU;sBAAC0G,OAAO,EAAC,IAAI;sBAACU,KAAK,EAAC,SAAS;sBAACT,YAAY;sBAAAtB,QAAA,EAClD,EAAAuD,UAAA,GAAAlD,OAAO,CAACM,EAAE,cAAA4C,UAAA,uBAAVA,UAAA,CAAYvB,OAAO,CAAC,CAAC,CAAC,KAAI;oBAAK;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC,eACb7D,OAAA,CAAC5B,UAAU;sBAAC0G,OAAO,EAAC,OAAO;sBAACU,KAAK,EAAC,gBAAgB;sBAAA/B,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb7D,OAAA,CAACrB,IAAI;sBACH+G,KAAK,EAAErB,mBAAmB,CAACP,OAAO,CAACM,EAAE,IAAI,CAAC,CAAE;sBAC5CuB,IAAI,EAAC,OAAO;sBACZH,KAAK,EAAErB,UAAU,CAACL,OAAO,CAACM,EAAE,IAAI,CAAC,CAAE;sBACnCO,EAAE,EAAE;wBAAEiB,EAAE,EAAE;sBAAE;oBAAE;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAEN7D,OAAA,CAACtB,OAAO;oBAACiG,EAAE,EAAE;sBAAEkB,EAAE,EAAE;oBAAE;kBAAE;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAE1B7D,OAAA,CAAC7B,GAAG;oBAACwG,EAAE,EAAE;sBAAEsD,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAErD,EAAE,EAAE;oBAAE,CAAE;oBAAApB,QAAA,gBACnEzD,OAAA,CAAC5B,UAAU;sBAAC0G,OAAO,EAAC,OAAO;sBAACU,KAAK,EAAC,gBAAgB;sBAAA/B,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb7D,OAAA,CAAC5B,UAAU;sBAAC0G,OAAO,EAAC,OAAO;sBAACqD,UAAU,EAAC,QAAQ;sBAAA1E,QAAA,GAC5C,EAAAwD,aAAA,GAAAnD,OAAO,CAACU,IAAI,cAAAyC,aAAA,uBAAZA,aAAA,CAAcxB,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,MACrC;oBAAA;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eAEN7D,OAAA,CAAC7B,GAAG;oBAACwG,EAAE,EAAE;sBAAEsD,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAErD,EAAE,EAAE;oBAAE,CAAE;oBAAApB,QAAA,gBACnEzD,OAAA,CAAC5B,UAAU;sBAAC0G,OAAO,EAAC,OAAO;sBAACU,KAAK,EAAC,gBAAgB;sBAAA/B,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb7D,OAAA,CAAC5B,UAAU;sBAAC0G,OAAO,EAAC,OAAO;sBAACqD,UAAU,EAAC,QAAQ;sBAAA1E,QAAA,GAC5C,EAAAyD,YAAA,GAAApD,OAAO,CAACW,GAAG,cAAAyC,YAAA,uBAAXA,YAAA,CAAazB,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,MACpC;oBAAA;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eAEN7D,OAAA,CAAC7B,GAAG;oBAACwG,EAAE,EAAE;sBAAEsD,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAErD,EAAE,EAAE;oBAAE,CAAE;oBAAApB,QAAA,gBACnEzD,OAAA,CAAC5B,UAAU;sBAAC0G,OAAO,EAAC,OAAO;sBAACU,KAAK,EAAC,gBAAgB;sBAAA/B,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb7D,OAAA,CAAC5B,UAAU;sBAAC0G,OAAO,EAAC,OAAO;sBAACqD,UAAU,EAAC,QAAQ;sBAAA1E,QAAA,GAC5C,EAAA0D,aAAA,GAAArD,OAAO,CAACY,IAAI,cAAAyC,aAAA,uBAAZA,aAAA,CAAc1B,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,GACrC;oBAAA;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,EAELkD,KAAK,KAAK,CAAC,iBACV/G,OAAA,CAAC7B,GAAG;oBAACwG,EAAE,EAAE;sBAAEiB,EAAE,EAAE;oBAAE,CAAE;oBAAAnC,QAAA,eACjBzD,OAAA,CAAC5B,UAAU;sBAAC0G,OAAO,EAAC,SAAS;sBAACU,KAAK,EAAC,cAAc;sBAAA/B,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC,GAlF6BiD,SAAS;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmFzC,CAAC;UAEX,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEP7D,OAAA,CAACtB,OAAO;UAACiG,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE;QAAE;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE1B7D,OAAA,CAAC5B,UAAU;UAAC0G,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAtB,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7D,OAAA,CAACnB,IAAI;UAAA4E,QAAA,EACF4C,YAAY,CAACQ,GAAG,CAAC,CAACC,SAAS,EAAEC,KAAK,KAAK;YAAA,IAAAqB,WAAA,EAAAC,cAAA;YACtC,MAAMvE,OAAO,GAAG5C,eAAe,CAAC4F,SAAS,CAAC;YAC1C,MAAMM,WAAW,GAAGN,SAAS,KAAKxG,SAAS;YAE3C,oBACEN,OAAA,CAAClB,QAAQ;cAAA2E,QAAA,gBACPzD,OAAA,CAACjB,YAAY;gBAAA0E,QAAA,eACXzD,OAAA,CAAC5B,UAAU;kBAAC0G,OAAO,EAAC,IAAI;kBAACU,KAAK,EAAEuB,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,gBAAiB;kBAAAtD,QAAA,EACxEsD,KAAK,GAAG;gBAAC;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACf7D,OAAA,CAAChB,YAAY;gBACX8G,OAAO,eACL9F,OAAA,CAAC7B,GAAG;kBAACwG,EAAE,EAAE;oBAAEsD,OAAO,EAAE,MAAM;oBAAEK,UAAU,EAAE,QAAQ;oBAAEC,GAAG,EAAE;kBAAE,CAAE;kBAAA9E,QAAA,gBACzDzD,OAAA,CAAC5B,UAAU;oBAAC0G,OAAO,EAAC,WAAW;oBAACqD,UAAU,EAAEf,WAAW,GAAG,MAAM,GAAG,QAAS;oBAAA3D,QAAA,EACzEqD,SAAS,CAACgB,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC;kBAAC;oBAAAtE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,EACZuD,WAAW,iBACVpH,OAAA,CAACrB,IAAI;oBAAC+G,KAAK,EAAC,MAAM;oBAACF,KAAK,EAAC,SAAS;oBAACG,IAAI,EAAC;kBAAO;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAClD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACDkC,SAAS,EAAE,aAAa,EAAAqC,WAAA,GAAAtE,OAAO,CAACM,EAAE,cAAAgE,WAAA,uBAAVA,WAAA,CAAY3C,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,YAAY,EAAA4C,cAAA,GAAAvE,OAAO,CAACU,IAAI,cAAA6D,cAAA,uBAAZA,cAAA,CAAc5C,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK;cAAO;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5G,CAAC,eACF7D,OAAA,CAAC7B,GAAG;gBAACwG,EAAE,EAAE;kBAAE6D,KAAK,EAAE,KAAK;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBAAAhF,QAAA,eAC/BzD,OAAA,CAAC7B,GAAG;kBACFwG,EAAE,EAAE;oBACF+D,MAAM,EAAE,CAAC;oBACTC,OAAO,EAAE5B,KAAK,KAAK,CAAC,GAAG,cAAc,GAAG,eAAe;oBACvDyB,KAAK,EAAE,GAAGI,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE,CAAChF,OAAO,CAACM,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG;oBACjE2E,QAAQ,EAAE,KAAK;oBACfC,YAAY,EAAE;kBAChB;gBAAE;kBAAAtF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GA7BOiD,SAAS;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8Bd,CAAC;UAEf,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAED,MAAMoF,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAACnI,iBAAiB,EAAE;MACtB,oBACEd,OAAA,CAACpB,KAAK;QAAC4E,QAAQ,EAAC,MAAM;QAAAC,QAAA,EAAC;MAEvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;;IAEA;IACA,MAAMqF,SAAS,GAAGpI,iBAAiB,CAAC4B,KAAK,GACvCuB,IAAI,CAACC,KAAK,CAACpD,iBAAiB,CAAC4B,KAAK,CAAC,GAAG,IAAI;IAE5C,IAAI,CAACwG,SAAS,EAAE;MACd,oBACElJ,OAAA,CAACpB,KAAK;QAAC4E,QAAQ,EAAC,SAAS;QAAAC,QAAA,EAAC;MAE1B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;;IAEA;IACA,IAAIsF,WAAW,GAAG,EAAE;IAEpB,IAAID,SAAS,CAACE,IAAI,IAAIF,SAAS,CAACE,IAAI,CAAChD,MAAM,GAAG,CAAC,EAAE;MAC/C,MAAMiD,UAAU,GAAGH,SAAS,CAACE,IAAI,CAAC,CAAC,CAAC;;MAEpC;MACA,IAAIC,UAAU,CAACC,CAAC,IAAID,UAAU,CAACE,CAAC,EAAE;QAChCJ,WAAW,GAAGE,UAAU,CAACE,CAAC,CAAC1C,GAAG,CAAC,CAAC2C,WAAW,EAAEzC,KAAK,MAAM;UACtD0C,IAAI,EAAED,WAAW;UACjBE,UAAU,EAAEL,UAAU,CAACC,CAAC,CAACvC,KAAK;QAChC,CAAC,CAAC,CAAC;MACL;IACF;;IAEA;IACA,IAAIoC,WAAW,CAAC/C,MAAM,KAAK,CAAC,IAAI8C,SAAS,CAACE,IAAI,EAAE;MAC9CD,WAAW,GAAGD,SAAS,CAACE,IAAI;IAC9B;;IAEA;IACA,IAAID,WAAW,CAAC/C,MAAM,KAAK,CAAC,EAAE;MAC5B,oBACEpG,OAAA,CAACpB,KAAK;QAAC4E,QAAQ,EAAC,SAAS;QAAAC,QAAA,EAAC;MAE1B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IAEA,oBACE7D,OAAA,CAAC7B,GAAG;MAAAsF,QAAA,eACFzD,OAAA,CAAC1B,KAAK;QAACqG,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,gBACzBzD,OAAA,CAAC5B,UAAU;UAAC0G,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAtB,QAAA,gBACnCzD,OAAA,CAACT,WAAW;YAACoF,EAAE,EAAE;cAAEK,EAAE,EAAE,CAAC;cAAEC,aAAa,EAAE;YAAS;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sBAEzD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7D,OAAA,CAACpB,KAAK;UAAC4E,QAAQ,EAAC,MAAM;UAACmB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAApB,QAAA,eACnCzD,OAAA,CAAC5B,UAAU;YAAC0G,OAAO,EAAC,OAAO;YAAArB,QAAA,EAAC;UAG5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAER7D,OAAA,CAACnB,IAAI;UAAA4E,QAAA,EACF0F,WAAW,CAACQ,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC9C,GAAG,CAAC,CAAC+C,OAAO,EAAE7C,KAAK,kBAC3C/G,OAAA,CAAClB,QAAQ;YAAA2E,QAAA,gBACPzD,OAAA,CAACjB,YAAY;cAAA0E,QAAA,eACXzD,OAAA,CAACR,UAAU;gBAACgG,KAAK,EAAEuB,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG;cAAS;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACf7D,OAAA,CAAChB,YAAY;cACX8G,OAAO,EAAE,GAAGiB,KAAK,GAAG,CAAC,KAAK6C,OAAO,CAACH,IAAI,IAAIG,OAAO,CAACN,CAAC,IAAI,WAAWvC,KAAK,GAAG,CAAC,EAAE,EAAG;cAChFhB,SAAS,EAAE,eAAe,CAAC,MAAM;gBAC/B,MAAM2D,UAAU,GAAGE,OAAO,CAACF,UAAU,IAAIE,OAAO,CAACL,CAAC;gBAClD,OAAO,OAAOG,UAAU,KAAK,QAAQ,GAAGA,UAAU,CAACjE,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;cACvE,CAAC,EAAE,CAAC;YAAG;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACF7D,OAAA,CAAC7B,GAAG;cAACwG,EAAE,EAAE;gBAAE6D,KAAK,EAAE,KAAK;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAhF,QAAA,eAC/BzD,OAAA,CAAC7B,GAAG;gBACFwG,EAAE,EAAE;kBACF+D,MAAM,EAAE,EAAE;kBACVC,OAAO,EAAE5B,KAAK,GAAG,CAAC,GAAG,cAAc,GAAG,eAAe;kBACrDyB,KAAK,EAAE,GAAGI,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,CAAC,QAAQc,OAAO,CAACF,UAAU,IAAIE,OAAO,CAACL,CAAC,CAAC,KAAK,QAAQ,GAAIK,OAAO,CAACF,UAAU,IAAIE,OAAO,CAACL,CAAC,GAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG;kBAC9IR,QAAQ,EAAE,IAAI;kBACdC,YAAY,EAAE;gBAChB;cAAE;gBAAAtF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GArBOkD,KAAK;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsBV,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEP7D,OAAA,CAACtB,OAAO;UAACiG,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE;QAAE;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE1B7D,OAAA,CAAC5B,UAAU;UAAC0G,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAtB,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7D,OAAA,CAACnB,IAAI;UAAA4E,QAAA,gBACHzD,OAAA,CAAClB,QAAQ;YAAA2E,QAAA,gBACPzD,OAAA,CAACjB,YAAY;cAAA0E,QAAA,eACXzD,OAAA,CAACP,IAAI;gBAAC+F,KAAK,EAAC;cAAS;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACf7D,OAAA,CAAChB,YAAY;cACX8G,OAAO,EAAC,gBAAgB;cACxBC,SAAS,EAAE;YAAwF;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAEX7D,OAAA,CAAClB,QAAQ;YAAA2E,QAAA,gBACPzD,OAAA,CAACjB,YAAY;cAAA0E,QAAA,eACXzD,OAAA,CAACP,IAAI;gBAAC+F,KAAK,EAAC;cAAS;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACf7D,OAAA,CAAChB,YAAY;cACX8G,OAAO,EAAC,mBAAmB;cAC3BC,SAAS,EAAC;YAAiF;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAED,MAAMgG,uBAAuB,GAAGA,CAAA,KAAM;IACpC,oBACE7J,OAAA,CAAC7B,GAAG;MAAAsF,QAAA,eACFzD,OAAA,CAAC1B,KAAK;QAACqG,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,gBACzBzD,OAAA,CAAC5B,UAAU;UAAC0G,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAtB,QAAA,gBACnCzD,OAAA,CAACV,QAAQ;YAACqF,EAAE,EAAE;cAAEK,EAAE,EAAE,CAAC;cAAEC,aAAa,EAAE;YAAS;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,8BAEtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7D,OAAA,CAACpB,KAAK;UAAC4E,QAAQ,EAAC,MAAM;UAACmB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAApB,QAAA,eACnCzD,OAAA,CAAC5B,UAAU;YAAC0G,OAAO,EAAC,OAAO;YAAArB,QAAA,EAAC;UAG5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAEP7C,cAAc,CAACoB,iBAAiB,gBAC/BpC,OAAA,CAAC7B,GAAG;UAACwG,EAAE,EAAE;YAAEmF,SAAS,EAAE,QAAQ;YAAElE,EAAE,EAAE;UAAE,CAAE;UAAAnC,QAAA,EACrC,OAAOzC,cAAc,CAACoB,iBAAiB,KAAK,QAAQ,gBACnDpC,OAAA;YACE+J,GAAG,EAAE,yBAAyB/I,cAAc,CAACoB,iBAAiB,EAAG;YACjE4H,GAAG,EAAC,yBAAyB;YAC7BC,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAExB,MAAM,EAAE;YAAO,CAAE;YAC5CyB,OAAO,EAAGC,CAAC,IAAK;cACdpI,OAAO,CAACL,KAAK,CAAC,yCAAyC,CAAC;cACxDyI,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAChC,OAAO,GAAG,MAAM;YACjC;UAAE;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACA7C,cAAc,CAACoB,iBAAiB,CAACO,IAAI,KAAK,QAAQ,gBACpD3C,OAAA,CAAC9B,IAAI;YACHkL,IAAI,EAAEnF,IAAI,CAACC,KAAK,CAAClD,cAAc,CAACoB,iBAAiB,CAACM,KAAK,CAAC,CAAC0G,IAAK;YAC9DkB,MAAM,EAAE;cACN,GAAGrG,IAAI,CAACC,KAAK,CAAClD,cAAc,CAACoB,iBAAiB,CAACM,KAAK,CAAC,CAAC4H,MAAM;cAC5DC,QAAQ,EAAE,IAAI;cACd7B,MAAM,EAAE;YACV,CAAE;YACF8B,MAAM,EAAE;cAAEC,UAAU,EAAE;YAAK,CAAE;YAC7BR,KAAK,EAAE;cAAEzB,KAAK,EAAE,MAAM;cAAEE,MAAM,EAAE;YAAQ;UAAE;YAAAhF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,gBAEF7D,OAAA,CAAC5B,UAAU;YAACoH,KAAK,EAAC,OAAO;YAAA/B,QAAA,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QACnE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAEN7D,OAAA,CAAC7B,GAAG;UAACwG,EAAE,EAAE;YAAEmF,SAAS,EAAE,QAAQ;YAAElF,CAAC,EAAE;UAAE,CAAE;UAAAnB,QAAA,gBACrCzD,OAAA,CAAC5B,UAAU;YAACoH,KAAK,EAAC,gBAAgB;YAAA/B,QAAA,EAC/B/C,SAAS,KAAK,CAAC,GAAG,0BAA0B,GAAG;UAA6B;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,EACZnD,SAAS,KAAK,CAAC,iBACdV,OAAA,CAAC3B,MAAM;YACLyG,OAAO,EAAC,UAAU;YAClB4F,OAAO,EAAEA,CAAA,KAAMrI,iBAAiB,CAAC,mBAAmB,CAAE;YACtDsC,EAAE,EAAE;cAAEiB,EAAE,EAAE;YAAE,CAAE;YAAAnC,QAAA,EACf;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAED,MAAM8G,mBAAmB,GAAGA,CAAA,KAAM;IAChC,oBACE3K,OAAA,CAAC7B,GAAG;MAAAsF,QAAA,eACFzD,OAAA,CAAC1B,KAAK;QAACqG,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,gBACzBzD,OAAA,CAAC5B,UAAU;UAAC0G,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAtB,QAAA,gBACnCzD,OAAA,CAACV,QAAQ;YAACqF,EAAE,EAAE;cAAEK,EAAE,EAAE,CAAC;cAAEC,aAAa,EAAE;YAAS;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sBAEtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7D,OAAA,CAACpB,KAAK;UAAC4E,QAAQ,EAAC,MAAM;UAACmB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAApB,QAAA,eACnCzD,OAAA,CAAC5B,UAAU;YAAC0G,OAAO,EAAC,OAAO;YAAArB,QAAA,EAAC;UAG5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAEP7C,cAAc,CAACsB,SAAS,gBACvBtC,OAAA,CAAC7B,GAAG;UAACwG,EAAE,EAAE;YAAEmF,SAAS,EAAE,QAAQ;YAAElE,EAAE,EAAE;UAAE,CAAE;UAAAnC,QAAA,EACrC,OAAOzC,cAAc,CAACsB,SAAS,KAAK,QAAQ,gBAC3CtC,OAAA;YACE+J,GAAG,EAAE,yBAAyB/I,cAAc,CAACsB,SAAS,EAAG;YACzD0H,GAAG,EAAC,gBAAgB;YACpBC,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAExB,MAAM,EAAE;YAAO,CAAE;YAC5CyB,OAAO,EAAGC,CAAC,IAAK;cACdpI,OAAO,CAACL,KAAK,CAAC,gCAAgC,CAAC;cAC/CyI,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAChC,OAAO,GAAG,MAAM;YACjC;UAAE;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACA7C,cAAc,CAACsB,SAAS,CAACK,IAAI,KAAK,QAAQ,gBAC5C3C,OAAA,CAAC9B,IAAI;YACHkL,IAAI,EAAEnF,IAAI,CAACC,KAAK,CAAClD,cAAc,CAACsB,SAAS,CAACI,KAAK,CAAC,CAAC0G,IAAK;YACtDkB,MAAM,EAAE;cACN,GAAGrG,IAAI,CAACC,KAAK,CAAClD,cAAc,CAACsB,SAAS,CAACI,KAAK,CAAC,CAAC4H,MAAM;cACpDC,QAAQ,EAAE,IAAI;cACd7B,MAAM,EAAE;YACV,CAAE;YACF8B,MAAM,EAAE;cAAEC,UAAU,EAAE;YAAK,CAAE;YAC7BR,KAAK,EAAE;cAAEzB,KAAK,EAAE,MAAM;cAAEE,MAAM,EAAE;YAAQ;UAAE;YAAAhF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,gBAEF7D,OAAA,CAAC5B,UAAU;YAACoH,KAAK,EAAC,OAAO;YAAA/B,QAAA,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QACnE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAEN7D,OAAA,CAAC7B,GAAG;UAACwG,EAAE,EAAE;YAAEmF,SAAS,EAAE,QAAQ;YAAElF,CAAC,EAAE;UAAE,CAAE;UAAAnB,QAAA,gBACrCzD,OAAA,CAAC5B,UAAU;YAACoH,KAAK,EAAC,gBAAgB;YAAA/B,QAAA,EAC/B/C,SAAS,KAAK,CAAC,GAAG,0BAA0B,GAAG;UAA6B;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,EACZnD,SAAS,KAAK,CAAC,iBACdV,OAAA,CAAC3B,MAAM;YACLyG,OAAO,EAAC,UAAU;YAClB4F,OAAO,EAAEA,CAAA,KAAMrI,iBAAiB,CAAC,WAAW,CAAE;YAC9CsC,EAAE,EAAE;cAAEiB,EAAE,EAAE;YAAE,CAAE;YAAAnC,QAAA,EACf;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,eAED7D,OAAA,CAACtB,OAAO;UAACiG,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE;QAAE;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE1B7D,OAAA,CAAC5B,UAAU;UAAC0G,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAtB,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7D,OAAA,CAACnB,IAAI;UAAA4E,QAAA,gBACHzD,OAAA,CAAClB,QAAQ;YAAA2E,QAAA,gBACPzD,OAAA,CAACjB,YAAY;cAAA0E,QAAA,eACXzD,OAAA,CAACP,IAAI;gBAAC+F,KAAK,EAAC;cAAS;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACf7D,OAAA,CAAChB,YAAY;cACX8G,OAAO,EAAC,qBAAqB;cAC7BC,SAAS,EAAC;YAA2E;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAEX7D,OAAA,CAAClB,QAAQ;YAAA2E,QAAA,gBACPzD,OAAA,CAACjB,YAAY;cAAA0E,QAAA,eACXzD,OAAA,CAACN,OAAO;gBAAC8F,KAAK,EAAC;cAAS;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACf7D,OAAA,CAAChB,YAAY;cACX8G,OAAO,EAAC,uBAAuB;cAC/BC,SAAS,EAAC;YAAgG;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3G,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAED,oBACE7D,OAAA,CAAC7B,GAAG;IAAAsF,QAAA,gBACFzD,OAAA,CAAC5B,UAAU;MAAC0G,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAtB,QAAA,EAAC;IAEtC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACb7D,OAAA,CAAC5B,UAAU;MAAC0G,OAAO,EAAC,OAAO;MAACU,KAAK,EAAC,gBAAgB;MAACoF,SAAS;MAAAnH,QAAA,EAAC;IAG7D;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb7D,OAAA,CAAC1B,KAAK;MAACqG,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAApB,QAAA,eACnBzD,OAAA,CAACd,IAAI;QACH2L,KAAK,EAAEnK,SAAU;QACjBoK,QAAQ,EAAE7I,eAAgB;QAC1B6C,OAAO,EAAC,YAAY;QACpBiG,aAAa,EAAC,MAAM;QACpBC,cAAc,EAAC,SAAS;QACxBC,SAAS,EAAC,SAAS;QAAAxH,QAAA,gBAEnBzD,OAAA,CAACf,GAAG;UAACiM,IAAI,eAAElL,OAAA,CAACX,QAAQ;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC6B,KAAK,EAAC;QAAY;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9C7D,OAAA,CAACf,GAAG;UAACiM,IAAI,eAAElL,OAAA,CAACR,UAAU;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC6B,KAAK,EAAC;QAAgB;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpD7D,OAAA,CAACf,GAAG;UAACiM,IAAI,eAAElL,OAAA,CAACV,QAAQ;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC6B,KAAK,EAAC;QAAa;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/C7D,OAAA,CAACf,GAAG;UAACiM,IAAI,eAAElL,OAAA,CAACT,WAAW;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC6B,KAAK,EAAC;QAAU;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/C7D,OAAA,CAACf,GAAG;UAACiM,IAAI,eAAElL,OAAA,CAACR,UAAU;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC6B,KAAK,EAAC;QAAW;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAEPnD,SAAS,KAAK,CAAC,IAAImC,wBAAwB,CAAC,CAAC,EAC7CnC,SAAS,KAAK,CAAC,IAAIsF,qBAAqB,CAAC,CAAC,EAC1CtF,SAAS,KAAK,CAAC,IAAImJ,uBAAuB,CAAC,CAAC,EAC5CnJ,SAAS,KAAK,CAAC,IAAIuI,uBAAuB,CAAC,CAAC,EAC5CvI,SAAS,KAAK,CAAC,IAAIiK,mBAAmB,CAAC,CAAC,eAEzC3K,OAAA,CAAC7B,GAAG;MAACwG,EAAE,EAAE;QAAEsD,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEtC,EAAE,EAAE;MAAE,CAAE;MAAAnC,QAAA,gBACnEzD,OAAA,CAAC3B,MAAM;QACLyG,OAAO,EAAC,UAAU;QAClB4F,OAAO,EAAErK,MAAO;QAChB8K,SAAS,eAAEnL,OAAA,CAACb,SAAS;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAC1B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET7D,OAAA,CAAC3B,MAAM;QACLyG,OAAO,EAAC,WAAW;QACnBU,KAAK,EAAC,SAAS;QACfkF,OAAO,EAAEtK,MAAO;QAChBgL,OAAO,eAAEpL,OAAA,CAACZ,YAAY;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAC3B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpD,EAAA,CAt1BIN,eAAe;AAAAkL,EAAA,GAAflL,eAAe;AAw1BrB,eAAeA,eAAe;AAAC,IAAAkL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}