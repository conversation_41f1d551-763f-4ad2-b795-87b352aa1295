<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evos Advanced Feature Selection - Pump Time Prediction</title>
    <link href="https://fonts.googleapis.com/css2?family=Blinker:wght@100;200;300;400;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Blinker', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .evos-logo {
            height: 50px;
            margin-bottom: 10px;
            filter: brightness(0) invert(1);
        }
        .evos-tagline {
            font-size: 16px;
            font-weight: 300;
            opacity: 0.9;
            margin-top: 5px;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .section h3 {
            margin: 0 0 15px 0;
            color: #333;
            display: flex;
            align-items: center;
        }
        .section-number {
            background: #FF6B35;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        .btn {
            background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background: linear-gradient(135deg, #E55A2B 0%, #FF7043 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature-card {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            background: white;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            border-color: #FF6B35;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .feature-card.selected {
            border-color: #28a745;
            background: #f8fff9;
        }
        .feature-card.excluded {
            border-color: #dc3545;
            background: #fff8f8;
            opacity: 0.7;
        }
        .feature-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }
        .feature-name {
            font-weight: bold;
            color: #333;
            flex-grow: 1;
        }
        .feature-checkbox {
            margin-left: 10px;
            transform: scale(1.2);
        }
        .feature-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        .feature-stats {
            font-size: 12px;
            color: #888;
        }
        .quality-score {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            margin-left: 10px;
        }
        .quality-excellent {
            background: #d4edda;
            color: #155724;
        }
        .quality-good {
            background: #d1ecf1;
            color: #0c5460;
        }
        .quality-fair {
            background: #fff3cd;
            color: #856404;
        }
        .quality-poor {
            background: #f8d7da;
            color: #721c24;
        }
        .stats-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid #ddd;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #FF6B35;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .transformation-controls {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .transformation-select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-left: 10px;
        }
        .results {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .warning {
            color: #ffc107;
        }
        .info {
            color: #17a2b8;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #FF6B35;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .category-section {
            margin-bottom: 25px;
        }
        .category-header {
            background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .category-controls {
            display: flex;
            gap: 10px;
        }
        .category-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .category-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        .recommendations {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        .recommendations h4 {
            margin: 0 0 10px 0;
            color: #0066cc;
        }
        .recommendations ul {
            margin: 0;
            padding-left: 20px;
        }
        .recommendations li {
            margin-bottom: 5px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="frontend/src/logo.png" alt="Evos Logo" class="evos-logo" onerror="this.style.display='none'">
            <h1>🔧 Evos Advanced Feature Selection</h1>
            <p>Analyze, select, and transform features for optimal model performance</p>
            <div class="evos-tagline">Let's Evolve Together</div>
        </div>
        
        <div class="content">
            <!-- Step 1: Load and Analyze -->
            <div class="section">
                <h3><span class="section-number">1</span>Load Data & Analyze Features</h3>
                <button class="btn" id="loadDataBtn">📁 Load PowerBI Data</button>
                <button class="btn btn-secondary" id="analyzeBtn" disabled>🔍 Analyze Features</button>
                <div class="results" id="loadResults" style="display: none;"></div>
            </div>

            <!-- Step 2: Feature Overview -->
            <div class="section" id="overviewSection" style="display: none;">
                <h3><span class="section-number">2</span>Feature Overview & Statistics</h3>
                <div class="stats-panel">
                    <div class="stats-grid" id="overviewStats"></div>
                </div>
                <div class="recommendations" id="recommendations"></div>
            </div>

            <!-- Step 3: Feature Selection -->
            <div class="section" id="selectionSection" style="display: none;">
                <h3><span class="section-number">3</span>Interactive Feature Selection</h3>
                <div class="category-section" id="featureCategories"></div>
                <div style="margin-top: 20px;">
                    <button class="btn btn-success" id="applySelectionBtn">✅ Apply Feature Selection</button>
                    <button class="btn btn-secondary" id="selectAllBtn">Select All</button>
                    <button class="btn btn-secondary" id="selectNoneBtn">Select None</button>
                    <button class="btn btn-secondary" id="selectRecommendedBtn">Select Recommended</button>
                </div>
                <div class="results" id="selectionResults" style="display: none;"></div>
            </div>

            <!-- Step 4: Train Model -->
            <div class="section" id="trainingSection" style="display: none;">
                <h3><span class="section-number">4</span>Train Model with Selected Features</h3>
                <div>
                    <label for="targetSelect">Select Target Variable:</label>
                    <select id="targetSelect" class="transformation-select">
                        <option value="pump_time">Pump Time</option>
                        <option value="pre_pump_time">Pre-Pump Time</option>
                        <option value="post_pump_time">Post-Pump Time</option>
                        <option value="terminal_time">Terminal Time</option>
                    </select>
                    <button class="btn" id="trainModelBtn" style="margin-left: 15px;">🤖 Train Model</button>
                </div>
                <div class="results" id="trainingResults" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        let featureAnalysis = null;
        let selectedFeatures = new Set();
        let featureTransformations = {};

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
        });

        function setupEventListeners() {
            document.getElementById('loadDataBtn').addEventListener('click', loadData);
            document.getElementById('analyzeBtn').addEventListener('click', analyzeFeatures);
            document.getElementById('applySelectionBtn').addEventListener('click', applyFeatureSelection);
            document.getElementById('selectAllBtn').addEventListener('click', () => selectFeatures('all'));
            document.getElementById('selectNoneBtn').addEventListener('click', () => selectFeatures('none'));
            document.getElementById('selectRecommendedBtn').addEventListener('click', () => selectFeatures('recommended'));
            document.getElementById('trainModelBtn').addEventListener('click', trainModel);
        }

        async function loadData() {
            showLoading('loadResults', 'Loading PowerBI data...');
            
            try {
                // Upload the file
                const formData = new FormData();
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = '.xlsx,.xls';
                
                fileInput.onchange = async function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        formData.append('file', file);
                        
                        const response = await fetch(`${API_BASE}/upload`, {
                            method: 'POST',
                            body: formData
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            showResults('loadResults', `
                                <div class="success">✅ Data loaded successfully!</div>
                                <div class="info">📊 Format: ${result.format_info.data_format}</div>
                                <div class="info">📏 Shape: ${result.format_info.shape[0]} rows × ${result.format_info.shape[1]} columns</div>
                            `);
                            
                            // Process the data
                            await processData();
                            
                            document.getElementById('analyzeBtn').disabled = false;
                        } else {
                            showResults('loadResults', `<div class="error">❌ Failed to load data: ${result.error}</div>`);
                        }
                    }
                };
                
                fileInput.click();
                
            } catch (error) {
                showResults('loadResults', `<div class="error">❌ Error: ${error.message}</div>`);
            }
        }

        async function processData() {
            try {
                // Validate data
                await fetch(`${API_BASE}/validate`, { method: 'POST' });
                
                // Process data
                await fetch(`${API_BASE}/process`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
                
            } catch (error) {
                console.error('Error processing data:', error);
            }
        }

        async function analyzeFeatures() {
            showLoading('loadResults', 'Analyzing features...');
            
            try {
                const response = await fetch(`${API_BASE}/features/analyze`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    featureAnalysis = result.analysis;
                    
                    showResults('loadResults', `
                        <div class="success">✅ Feature analysis completed!</div>
                        <div class="info">📊 ${Object.keys(featureAnalysis.feature_summary).length} features analyzed</div>
                        <div class="info">🎯 Data quality: ${featureAnalysis.data_quality.completeness_percentage}% complete</div>
                    `);
                    
                    displayFeatureOverview();
                    displayFeatureSelection();
                    
                    document.getElementById('overviewSection').style.display = 'block';
                    document.getElementById('selectionSection').style.display = 'block';
                    document.getElementById('trainingSection').style.display = 'block';
                    
                } else {
                    showResults('loadResults', `<div class="error">❌ Analysis failed: ${result.error}</div>`);
                }
                
            } catch (error) {
                showResults('loadResults', `<div class="error">❌ Error: ${error.message}</div>`);
            }
        }

        function displayFeatureOverview() {
            const stats = featureAnalysis.data_quality;
            const overviewStats = document.getElementById('overviewStats');
            
            overviewStats.innerHTML = `
                <div class="stat-item">
                    <div class="stat-value">${stats.total_features}</div>
                    <div class="stat-label">Total Features</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${stats.total_rows}</div>
                    <div class="stat-label">Data Rows</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${stats.completeness_percentage}%</div>
                    <div class="stat-label">Data Completeness</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${stats.numeric_features}</div>
                    <div class="stat-label">Numeric Features</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${stats.categorical_features}</div>
                    <div class="stat-label">Categorical Features</div>
                </div>
            `;
            
            // Display recommendations
            const recommendations = document.getElementById('recommendations');
            recommendations.innerHTML = `
                <h4>💡 Recommendations</h4>
                <ul>
                    ${featureAnalysis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                </ul>
            `;
        }

        function displayFeatureSelection() {
            const categories = featureAnalysis.categories;
            const featureCategories = document.getElementById('featureCategories');
            
            featureCategories.innerHTML = '';
            
            for (const [category, features] of Object.entries(categories)) {
                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'category-section';
                
                categoryDiv.innerHTML = `
                    <div class="category-header">
                        <span>${category.toUpperCase()} FEATURES (${features.length})</span>
                        <div class="category-controls">
                            <button class="category-btn" onclick="selectCategoryFeatures('${category}', true)">Select All</button>
                            <button class="category-btn" onclick="selectCategoryFeatures('${category}', false)">Deselect All</button>
                        </div>
                    </div>
                    <div class="feature-grid" id="category-${category}"></div>
                `;
                
                featureCategories.appendChild(categoryDiv);
                
                const featureGrid = document.getElementById(`category-${category}`);
                
                features.forEach(feature => {
                    const featureInfo = featureAnalysis.feature_summary[feature];
                    const featureCard = createFeatureCard(feature, featureInfo);
                    featureGrid.appendChild(featureCard);
                });
            }
            
            // Initialize with recommended features selected
            selectFeatures('recommended');
        }

        function createFeatureCard(feature, info) {
            const card = document.createElement('div');
            card.className = 'feature-card';
            card.id = `feature-${feature}`;
            
            const qualityClass = getQualityClass(info.data_quality_score);
            const isRecommended = info.recommendation === 'include';
            
            card.innerHTML = `
                <div class="feature-header">
                    <div class="feature-name">
                        ${feature}
                        <span class="quality-score ${qualityClass}">${info.data_quality_score}/100</span>
                    </div>
                    <input type="checkbox" class="feature-checkbox" 
                           onchange="toggleFeature('${feature}')" 
                           ${isRecommended ? 'checked' : ''}>
                </div>
                <div class="feature-description">${info.description}</div>
                <div class="feature-stats">
                    Type: ${info.feature_type} | 
                    Missing: ${info.missing_percentage}% | 
                    Unique: ${info.unique_count}
                    ${info.feature_type === 'numeric' ? 
                        `<br>Mean: ${info.statistics?.mean?.toFixed(2) || 'N/A'} | 
                         Outliers: ${info.outlier_percentage || 0}%` : 
                        `<br>Most common: ${info.most_common || 'N/A'} (${info.most_common_percentage || 0}%)`
                    }
                </div>
                <div class="transformation-controls" style="display: none;">
                    <label>Transform:</label>
                    <select class="transformation-select" onchange="setTransformation('${feature}', this.value)">
                        <option value="">None</option>
                        <option value="standardize">Standardize</option>
                        <option value="normalize">Normalize</option>
                        <option value="log_transform">Log Transform</option>
                        <option value="remove_outliers">Remove Outliers</option>
                    </select>
                </div>
            `;
            
            if (isRecommended) {
                selectedFeatures.add(feature);
                card.classList.add('selected');
            }
            
            return card;
        }

        function getQualityClass(score) {
            if (score >= 80) return 'quality-excellent';
            if (score >= 60) return 'quality-good';
            if (score >= 40) return 'quality-fair';
            return 'quality-poor';
        }

        function toggleFeature(feature) {
            const card = document.getElementById(`feature-${feature}`);
            const checkbox = card.querySelector('.feature-checkbox');
            
            if (checkbox.checked) {
                selectedFeatures.add(feature);
                card.classList.add('selected');
                card.classList.remove('excluded');
            } else {
                selectedFeatures.delete(feature);
                card.classList.remove('selected');
                card.classList.add('excluded');
            }
            
            updateSelectionSummary();
        }

        function selectFeatures(mode) {
            const checkboxes = document.querySelectorAll('.feature-checkbox');
            
            checkboxes.forEach(checkbox => {
                const feature = checkbox.onchange.toString().match(/'([^']+)'/)[1];
                const featureInfo = featureAnalysis.feature_summary[feature];
                
                let shouldSelect = false;
                
                if (mode === 'all') {
                    shouldSelect = true;
                } else if (mode === 'none') {
                    shouldSelect = false;
                } else if (mode === 'recommended') {
                    shouldSelect = featureInfo.recommendation === 'include';
                }
                
                checkbox.checked = shouldSelect;
                toggleFeature(feature);
            });
        }

        function selectCategoryFeatures(category, select) {
            const categoryGrid = document.getElementById(`category-${category}`);
            const checkboxes = categoryGrid.querySelectorAll('.feature-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = select;
                const feature = checkbox.onchange.toString().match(/'([^']+)'/)[1];
                toggleFeature(feature);
            });
        }

        function setTransformation(feature, transformation) {
            if (transformation) {
                featureTransformations[feature] = transformation;
            } else {
                delete featureTransformations[feature];
            }
        }

        function updateSelectionSummary() {
            // Could add a summary display here
        }

        async function applyFeatureSelection() {
            if (selectedFeatures.size === 0) {
                showResults('selectionResults', '<div class="error">❌ Please select at least one feature</div>');
                return;
            }
            
            showLoading('selectionResults', 'Applying feature selection...');
            
            try {
                const response = await fetch(`${API_BASE}/features/select`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        selected_features: Array.from(selectedFeatures),
                        transformations: featureTransformations
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResults('selectionResults', `
                        <div class="success">✅ Feature selection applied successfully!</div>
                        <div class="info">📊 Selected features: ${result.selected_features.length}</div>
                        <div class="info">🔧 Transformations applied: ${result.transformations_applied.length}</div>
                        <div class="info">📏 Final data shape: ${result.processed_shape[0]} rows × ${result.processed_shape[1]} columns</div>
                    `);
                } else {
                    showResults('selectionResults', `<div class="error">❌ Feature selection failed: ${result.error}</div>`);
                }
                
            } catch (error) {
                showResults('selectionResults', `<div class="error">❌ Error: ${error.message}</div>`);
            }
        }

        async function trainModel() {
            const target = document.getElementById('targetSelect').value;
            
            showLoading('trainingResults', 'Training model with selected features...');
            
            try {
                const response = await fetch(`${API_BASE}/train`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        target_column: target,
                        algorithms: ['random_forest'],
                        hyperparameter_tuning: false
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const metrics = result.evaluation_results[result.best_model];
                    const r2Score = metrics.r2 !== undefined ? metrics.r2 : null;
                    const rmse = metrics.rmse !== undefined ? metrics.rmse : null;
                    
                    let r2Interpretation = '';
                    if (r2Score !== null) {
                        if (r2Score >= 0.9) r2Interpretation = ' (Excellent! 🎯)';
                        else if (r2Score >= 0.7) r2Interpretation = ' (Good 👍)';
                        else if (r2Score >= 0.5) r2Interpretation = ' (Moderate ⚠️)';
                        else if (r2Score >= 0.0) r2Interpretation = ' (Poor 📈)';
                        else r2Interpretation = ' (Very poor 🔧)';
                    }
                    
                    showResults('trainingResults', `
                        <div class="success">✅ Model trained successfully!</div>
                        <div class="info">🤖 Best model: ${result.best_model}</div>
                        <div class="info">📊 R² Score: ${r2Score !== null ? r2Score.toFixed(3) : 'N/A'}${r2Interpretation}</div>
                        <div class="info">📏 RMSE: ${rmse !== null ? rmse.toFixed(2) : 'N/A'} minutes</div>
                        <div class="info">🎯 Target: ${target.replace('_', ' ').toUpperCase()}</div>
                        <div class="info">🔧 Features used: ${selectedFeatures.size}</div>
                    `);
                } else {
                    showResults('trainingResults', `<div class="error">❌ Training failed: ${result.error}</div>`);
                }
                
            } catch (error) {
                showResults('trainingResults', `<div class="error">❌ Error: ${error.message}</div>`);
            }
        }

        function showLoading(elementId, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="info"><span class="loading"></span>${message}</div>`;
            element.style.display = 'block';
        }

        function showResults(elementId, content) {
            const element = document.getElementById(elementId);
            element.innerHTML = content;
            element.style.display = 'block';
        }
    </script>
</body>
</html>
