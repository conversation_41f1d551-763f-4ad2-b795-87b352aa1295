/*************************************************************
 *
 *  MathJax/localization/bg/TeX.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("bg","TeX",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          ExtraOpenMissingClose: "\u0418\u043C\u0430 \u0438\u0437\u043B\u0438\u0448\u043D\u0430 \u043E\u0442\u0432\u0430\u0440\u044F\u0449\u0430 \u0438\u043B\u0438 \u043B\u0438\u043F\u0441\u0432\u0430\u0449\u0430 \u0437\u0430\u0442\u0432\u0430\u0440\u044F\u0449\u0430 \u0441\u043A\u043E\u0431\u0430",
          ExtraCloseMissingOpen: "\u0418\u043C\u0430 \u0438\u0437\u043B\u0438\u0448\u043D\u0430 \u0437\u0430\u0442\u0432\u0430\u0440\u044F\u0449\u0430 \u0438\u043B\u0438 \u043B\u0438\u043F\u0441\u0432\u0430\u0449\u0430 \u043E\u0442\u0432\u0430\u0440\u044F\u0449\u0430 \u0441\u043A\u043E\u0431\u0430",
          MissingLeftExtraRight: "\u041B\u0438\u043F\u0441\u0432\u0430 \\left \u0438\u043B\u0438 \u0438\u043C\u0430 \u0438\u0437\u043B\u0438\u0448\u0435\u043D \\right",
          MissingScript: "\u041B\u0438\u043F\u0441\u0432\u0430 \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442 \u043F\u0440\u0438 \u0441\u0442\u0435\u043F\u0435\u043D \u0438\u043B\u0438 \u0438\u043D\u0434\u0435\u043A\u0441",
          ExtraLeftMissingRight: "\u0418\u043C\u0430 \u0438\u0437\u043B\u0438\u0448\u0435\u043D \\left \u0438\u043B\u0438 \u043B\u0438\u043F\u0441\u0432\u0430 \\right",
          Misplaced: "\u041D\u0435\u043F\u0440\u0430\u0432\u0438\u043B\u043D\u043E \u043F\u043E\u0441\u0442\u0430\u0432\u0435\u043D %1",
          MissingOpenForSub: "\u041B\u0438\u043F\u0441\u0432\u0430 \u043E\u0442\u0432\u0430\u0440\u044F\u0449\u0430 \u0441\u043A\u043E\u0431\u0430 \u0437\u0430 \u0438\u043D\u0434\u0435\u043A\u0441",
          MissingOpenForSup: "\u041B\u0438\u043F\u0441\u0432\u0430 \u043E\u0442\u0432\u0430\u0440\u044F\u0449\u0430 \u0441\u043A\u043E\u0431\u0430 \u0437\u0430 \u0441\u0442\u0435\u043F\u0435\u043D",
          AmbiguousUseOf: "\u0414\u0432\u0443\u0441\u043C\u0438\u0441\u043B\u0435\u043D\u0430 \u0443\u043F\u043E\u0442\u0440\u0435\u0431\u0430 \u043D\u0430 %1",
          EnvBadEnd: "\\begin{%1} \u0435 \u0437\u0430\u0442\u0432\u043E\u0440\u0435\u043D \u0441 \\end{%2}",
          EnvMissingEnd: "\u041B\u0438\u043F\u0441\u0432\u0430 \\end{%1}",
          MissingBoxFor: "\u041B\u0438\u043F\u0441\u0432\u0430\u0449 box \u0437\u0430 %1",
          MissingCloseBrace: "\u041B\u0438\u043F\u0441\u0432\u0430 \u0437\u0430\u0442\u0432\u0430\u0440\u044F\u0449\u0430 \u0441\u043A\u043E\u0431\u0430",
          UndefinedControlSequence: "\u041D\u0435\u043E\u043F\u0440\u0435\u0434\u0435\u043B\u0435\u043D\u0430 \u043A\u043E\u043D\u0442\u0440\u043E\u043B\u043D\u0430 \u043F\u043E\u0441\u043B\u0435\u0434\u043E\u0432\u0430\u0442\u0435\u043B\u043D\u043E\u0441\u0442 %1",
          DoubleExponent: "\u0414\u0432\u043E\u0439\u043D\u0430 \u0435\u043A\u0441\u043F\u043E\u043C\u0435\u043D\u0442\u0430: \u0443\u0442\u043E\u0447\u043D\u0435\u0442\u0435 \u0441\u044A\u0441 \u0441\u043A\u043E\u0431\u0438",
          DoubleSubscripts: "\u0414\u0432\u043E\u0435\u043D \u0438\u043D\u0434\u0435\u043A\u0441: \u0443\u0442\u043E\u0447\u043D\u0435\u0442\u0435 \u0441\u044A\u0441 \u0441\u043A\u043E\u0431\u0438",
          DoubleExponentPrime: "\u0414\u0432\u0443\u0441\u043C\u0438\u0441\u043B\u0435\u043D\u0430 \u0443\u043F\u043E\u0442\u0440\u0435\u0431\u0430 \u043D\u0430 \u043F\u0440\u0438\u043C ('): \u0443\u0442\u043E\u0447\u043D\u0435\u0442\u0435 \u0441\u044A\u0441 \u0441\u043A\u043E\u0431\u0438",
          CantUseHash1: "\u041D\u0435 \u043C\u043E\u0436\u0435 \u0434\u0430 \u0438\u0437\u043F\u043E\u043B\u0437\u0432\u0430\u0442\u0435 '\u043C\u0430\u043A\u0440\u043E \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u044A\u0440 #' \u0432 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u0435\u0441\u043A\u0438 \u0440\u0435\u0436\u0438\u043C",
          MisplacedMiddle: "%1 \u0442\u0440\u044F\u0431\u0432\u0430 \u0434\u0430 \u0435 \u043C\u0435\u0436\u0434\u0443 \\left \u0438 \\right",
          MisplacedLimits: "%1 \u0435 \u043F\u043E\u0437\u0432\u043E\u043B\u0435\u043D\u043E \u0441\u0430\u043C\u043E \u043F\u0440\u0438 \u043E\u043F\u0435\u0440\u0430\u0442\u043E\u0440\u0438",
          MisplacedMoveRoot: "%1 \u0441\u0435 \u0438\u0437\u043F\u043E\u043B\u0437\u0432\u0430 \u0441\u0430\u043C\u043E \u0432 \u043A\u043E\u0440\u0435\u043D",
          MultipleCommand: "\u041C\u043D\u043E\u0436\u0435\u0441\u0442\u0432\u043E %1",
          IntegerArg: "\u0410\u0440\u0433\u0443\u043C\u0435\u043D\u0442\u044A\u0442 \u0437\u0430 %1 \u0442\u0440\u044F\u0431\u0432\u0430 \u0434\u0430 \u0435 \u0446\u044F\u043B\u043E \u0447\u0438\u0441\u043B\u043E",
          NotMathMLToken: "%1 - \u043D\u0435 \u0435 \u0437\u0430 MathML",
          InvalidMathMLAttr: "\u041D\u0435\u0432\u0430\u043B\u0438\u0434\u0435\u043D MathML \u0430\u0442\u0440\u0438\u0431\u0443\u0442: %1",
          UnknownAttrForElement: "%1 \u043D\u0435 \u0435 \u0430\u0442\u0440\u0438\u0431\u0443\u0442 \u043D\u0430 MathML \u0442\u0430\u0433 %2",
          MaxMacroSub1: "\u041C\u0430\u043A\u0441\u0438\u043C\u0430\u043B\u043D\u0438\u044F\u0442 \u0431\u0440\u043E\u0439 \u0437\u0430\u043C\u0435\u0441\u0442\u0432\u0430\u043D\u0438\u044F \u0441 \u043C\u0430\u043A\u0440\u043E\u0441\u0438 \u0435 \u043F\u0440\u0435\u0432\u0438\u0448\u0435\u043D. \u0418\u043C\u0430 \u043B\u0438 \u0440\u0435\u043A\u0443\u0440\u0441\u0438\u0432\u043D\u043E \u0438\u0437\u0432\u0438\u043A\u0432\u0430\u043D\u0435?",
          MaxMacroSub2: "\u041C\u0430\u043A\u0441\u0438\u043C\u0430\u043B\u043D\u0438\u044F\u0442 \u0431\u0440\u043E\u0439 \u043D\u0430 \u0437\u0430\u043C\u0435\u0441\u0442\u0432\u0430\u043D\u0438\u044F \u0435 \u043F\u0440\u0435\u0432\u0438\u0448\u0435\u043D. \u0418\u043C\u0430 \u043B\u0438 \u0440\u0435\u043A\u0443\u0440\u0441\u0438\u044F?",
          MissingArgFor: "\u041B\u0438\u043F\u0441\u0432\u0430 \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442 \u0437\u0430 %1",
          ExtraAlignTab: "\u0422\u0432\u044A\u0440\u0434\u0435 \u043C\u043D\u043E\u0433\u043E \u043A\u043E\u043B\u043E\u043D\u0438 \u0432 \\cases",
          BracketMustBeDimension: "\u0410\u0440\u0433\u0443\u043C\u0435\u043D\u0442\u044A\u0442 \u043D\u0430 %1 \u0442\u0440\u044F\u0431\u0432\u0430 \u0434\u0430 \u0435 \u0440\u0430\u0437\u043C\u0435\u0440\u043D\u043E\u0441\u0442",
          InvalidEnv: "\u041D\u0435\u0432\u0430\u043B\u0438\u0434\u043D\u043E \u0438\u043C\u0435 '%1'",
          UnknownEnv: "\u041D\u0435\u043F\u043E\u0437\u043D\u0430\u0442\u0430 \u0441\u0440\u0435\u0434\u0430 '%1'",
          ExtraCloseLooking: "\u0418\u0437\u043B\u0438\u0448\u043D\u0430 \u0437\u0430\u0442\u0432\u0430\u0440\u044F\u0449\u0430 \u0441\u043A\u043E\u0431\u0430 \u043F\u0440\u0438 \u0442\u044A\u0440\u0441\u0435\u043D\u0435 \u043D\u0430 %1",
          MissingCloseBracket: "\u041D\u044F\u043C\u0430 \u0437\u0430\u0442\u0432\u0430\u0440\u044F\u0449\u0430 \u0441\u043A\u043E\u0431\u0430 ']' \u0437\u0430 \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442 \u043D\u0430 %1",
          MissingOrUnrecognizedDelim: "\u041B\u0438\u043F\u0441\u0432\u0430\u0449 \u0438\u043B\u0438 \u043D\u0435\u043F\u043E\u0437\u043D\u0430\u0442 \u0440\u0430\u0437\u0434\u0435\u043B\u0438\u0442\u0435\u043B \u043D\u0430 %1",
          MissingDimOrUnits: "\u041D\u0435\u0432\u0430\u043B\u0438\u0434\u043D\u0430 \u0440\u0430\u0437\u043C\u0435\u0440\u043D\u043E\u0441\u0442 \u0438\u043B\u0438 \u043D\u0435\u0439\u043D\u0430 \u0441\u0442\u043E\u0439\u043D\u043E\u0441\u0442 \u0432 %1",
          TokenNotFoundForCommand: "\u041D\u0435 \u0441\u0435 \u043D\u0430\u043C\u0438\u0440\u0430 %1 \u0437\u0430 %2",
          CommandNotAllowedInEnv: "%1 \u043D\u0435 \u0435 \u043F\u043E\u0437\u0432\u043E\u043B\u0435\u043D\u043E \u0432 %2 \u0441\u0440\u0435\u0434\u0430",
          MultipleLabel: "\u0415\u0442\u0438\u043A\u0435\u0442 \"%1\" \u0435 \u0434\u0443\u0431\u043B\u0438\u0440\u0430\u043D",
          InvalidNumber: "\u041D\u0435\u0432\u0430\u043B\u0438\u0434\u043D\u043E \u0447\u0438\u0441\u043B\u043E"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/bg/TeX.js");
