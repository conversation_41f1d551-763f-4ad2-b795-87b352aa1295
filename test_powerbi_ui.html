<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evos PowerBI Pump Time Prediction - Test Interface</title>
    <link href="https://fonts.googleapis.com/css2?family=Blinker:wght@100;200;300;400;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Blinker', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #26a69a 0%, #00acc1 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .evos-logo {
            height: 50px;
            margin-bottom: 10px;
            filter: brightness(0) invert(1);
        }
        .evos-tagline {
            font-size: 16px;
            font-weight: 300;
            opacity: 0.9;
            margin-top: 5px;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .step {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .step.active {
            border-color: #26a69a;
            background: #e0f2f1;
        }
        .step.completed {
            border-color: #4caf50;
            background: #f1f8e9;
        }
        .step h3 {
            margin: 0 0 15px 0;
            color: #333;
            display: flex;
            align-items: center;
        }
        .step-number {
            background: #26a69a;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        .step.completed .step-number {
            background: #4caf50;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #26a69a;
            background: #e0f2f1;
        }
        .upload-area.dragover {
            border-color: #26a69a;
            background: #e0f2f1;
        }
        .btn {
            background: linear-gradient(135deg, #26a69a 0%, #00acc1 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: linear-gradient(135deg, #00897b 0%, #0097a7 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(38, 166, 154, 0.3);
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .results {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .warning {
            color: #ffc107;
        }
        .info {
            color: #17a2b8;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #26a69a;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .target-selection {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .target-option {
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s ease;
        }
        .target-option:hover {
            border-color: #26a69a;
        }
        .target-option.selected {
            border-color: #26a69a;
            background: #e0f2f1;
        }
        .prediction-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
        }
        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-group input, .form-group select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .prediction-result {
            background: linear-gradient(135deg, #26a69a 0%, #00acc1 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-top: 15px;
        }
        .prediction-result h4 {
            margin: 0 0 10px 0;
        }
        .prediction-value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="frontend/public/logo.png" alt="Evos Logo" class="evos-logo" onerror="this.style.display='none'">
            <h1>🚢 Evos PowerBI Pump Time Prediction</h1>
            <p>Upload your PowerBI export and build predictive models automatically</p>
            <div class="evos-tagline">Let's Evolve Together</div>
        </div>
        
        <div class="content">
            <!-- Step 1: Upload -->
            <div class="step" id="step1">
                <h3><span class="step-number">1</span>Upload PowerBI Export</h3>
                <div class="upload-area" id="uploadArea">
                    <p>📁 Drop your Excel file here or click to browse</p>
                    <p style="font-size: 14px; color: #666;">Supports PowerBI exports (.xlsx)</p>
                    <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;">
                </div>
                <div class="results" id="uploadResults" style="display: none;"></div>
            </div>

            <!-- Step 2: Validate -->
            <div class="step" id="step2">
                <h3><span class="step-number">2</span>Validate Data</h3>
                <button class="btn" id="validateBtn" disabled>🔍 Validate Data Quality</button>
                <div class="results" id="validateResults" style="display: none;"></div>
            </div>

            <!-- Step 3: Process -->
            <div class="step" id="step3">
                <h3><span class="step-number">3</span>Process Data</h3>
                <button class="btn" id="processBtn" disabled>⚙️ Process & Clean Data</button>
                <div class="results" id="processResults" style="display: none;"></div>
            </div>

            <!-- Step 4: Train -->
            <div class="step" id="step4">
                <h3><span class="step-number">4</span>Train Model</h3>
                <div id="targetSelection" style="display: none;">
                    <p>Select target variable to predict:</p>
                    <div class="target-selection" id="targetOptions"></div>
                </div>
                <button class="btn" id="trainBtn" disabled>🤖 Train Model</button>
                <div class="results" id="trainResults" style="display: none;"></div>
            </div>

            <!-- Step 5: Predict -->
            <div class="step" id="step5">
                <h3><span class="step-number">5</span>Make Predictions</h3>
                <div id="predictionForm" style="display: none;">
                    <div class="prediction-form">
                        <div class="form-group">
                            <label>Product Quantity (tons)</label>
                            <input type="number" id="productQuantity" value="1500" min="0" step="0.1">
                        </div>
                        <div class="form-group">
                            <label>Vessel Type</label>
                            <select id="vesselType">
                                <option value="Barge">Barge</option>
                                <option value="Vessel">Vessel</option>
                                <option value="Pipeline">Pipeline</option>
                                <option value="Truck">Truck</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Product Type</label>
                            <select id="productType">
                                <option value="Gasoline">Gasoline</option>
                                <option value="Diesel">Diesel</option>
                                <option value="Crude Oil">Crude Oil</option>
                                <option value="Jet Fuel">Jet Fuel</option>
                                <option value="Chemical">Chemical</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Operation Type</label>
                            <select id="operationType">
                                <option value="Loading">Loading</option>
                                <option value="Discharging">Discharging</option>
                            </select>
                        </div>
                    </div>
                    <button class="btn" id="predictBtn" style="margin-top: 15px;">🎯 Make Prediction</button>
                </div>
                <div id="predictionResult" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        let selectedTarget = 'pump_time';
        let currentStep = 1;

        // File upload handling
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');

        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileUpload(e.target.files[0]);
            }
        });

        async function handleFileUpload(file) {
            const formData = new FormData();
            formData.append('file', file);

            showLoading('uploadResults', 'Uploading file...');
            
            try {
                const response = await fetch(`${API_BASE}/upload`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResults('uploadResults', `
                        <div class="success">✅ File uploaded successfully!</div>
                        <div class="info">📊 Data format: ${result.format_info.data_format}</div>
                        <div class="info">📏 Shape: ${result.format_info.shape[0]} rows × ${result.format_info.shape[1]} columns</div>
                        ${result.format_info.available_targets ? 
                            `<div class="info">🎯 Available targets: ${result.format_info.available_targets.join(', ')}</div>` : ''}
                    `);
                    completeStep(1);
                    enableStep(2);
                } else {
                    showResults('uploadResults', `<div class="error">❌ Upload failed: ${result.errors.join(', ')}</div>`);
                }
            } catch (error) {
                showResults('uploadResults', `<div class="error">❌ Upload failed: ${error.message}</div>`);
            }
        }

        // Validation
        document.getElementById('validateBtn').addEventListener('click', async () => {
            showLoading('validateResults', 'Validating data...');
            
            try {
                const response = await fetch(`${API_BASE}/validate`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.validation_result && result.validation_result.is_valid) {
                    showResults('validateResults', `
                        <div class="success">✅ Data validation passed!</div>
                        <div class="info">📊 Data completeness: ${result.feature_summary.data_quality.completeness_score}%</div>
                        <div class="info">🔧 Usable features: ${result.feature_summary.data_quality.usable_features}</div>
                        <div class="info">🎯 Available targets: ${result.available_targets.join(', ')}</div>
                        <div class="info">💡 Recommended target: ${result.feature_summary.data_quality.recommended_target}</div>
                    `);
                    completeStep(2);
                    enableStep(3);
                } else {
                    showResults('validateResults', `<div class="error">❌ Validation failed: ${result.error || 'Unknown error'}</div>`);
                }
            } catch (error) {
                showResults('validateResults', `<div class="error">❌ Validation failed: ${error.message}</div>`);
            }
        });

        // Processing
        document.getElementById('processBtn').addEventListener('click', async () => {
            showLoading('processResults', 'Processing data...');
            
            try {
                const response = await fetch(`${API_BASE}/process`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResults('processResults', `
                        <div class="success">✅ Data processed successfully!</div>
                        <div class="info">📊 Final shape: ${result.processing_summary.final_shape[0]} rows × ${result.processing_summary.final_shape[1]} columns</div>
                        <div class="info">🔧 Format: ${result.processing_summary.data_format}</div>
                        ${result.processing_summary.available_targets ? 
                            `<div class="info">🎯 Available targets: ${result.processing_summary.available_targets.join(', ')}</div>` : ''}
                    `);
                    completeStep(3);
                    enableStep(4);
                    setupTargetSelection(result.processing_summary.available_targets || ['pump_time', 'pre_pump_time', 'post_pump_time']);
                } else {
                    showResults('processResults', `<div class="error">❌ Processing failed: ${result.error}</div>`);
                }
            } catch (error) {
                showResults('processResults', `<div class="error">❌ Processing failed: ${error.message}</div>`);
            }
        });

        function setupTargetSelection(targets) {
            const targetSelection = document.getElementById('targetSelection');
            const targetOptions = document.getElementById('targetOptions');
            
            targetOptions.innerHTML = '';
            targets.forEach(target => {
                const option = document.createElement('div');
                option.className = 'target-option';
                option.textContent = target.replace('_', ' ').toUpperCase();
                option.addEventListener('click', () => {
                    document.querySelectorAll('.target-option').forEach(opt => opt.classList.remove('selected'));
                    option.classList.add('selected');
                    selectedTarget = target;
                });
                
                if (target === 'pump_time') {
                    option.classList.add('selected');
                }
                
                targetOptions.appendChild(option);
            });
            
            targetSelection.style.display = 'block';
        }

        // Training
        document.getElementById('trainBtn').addEventListener('click', async () => {
            showLoading('trainResults', 'Training model...');
            
            try {
                const response = await fetch(`${API_BASE}/train`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        target_column: selectedTarget,
                        algorithms: ['random_forest'],
                        hyperparameter_tuning: false
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const metrics = result.evaluation_results[result.best_model];

                    // Get the correct R² score (it's stored as 'r2', not 'r2_score')
                    const r2Score = metrics.r2 !== undefined ? metrics.r2 : null;
                    const rmse = metrics.rmse !== undefined ? metrics.rmse : null;
                    const mae = metrics.mae !== undefined ? metrics.mae : null;
                    const mape = metrics.mape !== undefined ? metrics.mape : null;

                    // Interpret R² score and provide recommendations
                    let r2Interpretation = '';
                    let recommendations = [];

                    if (r2Score !== null) {
                        if (r2Score >= 0.9) {
                            r2Interpretation = ' (Excellent fit! 🎯)';
                            recommendations.push('Model is performing excellently!');
                        } else if (r2Score >= 0.7) {
                            r2Interpretation = ' (Good fit 👍)';
                            recommendations.push('Good model performance. Consider fine-tuning for better results.');
                        } else if (r2Score >= 0.5) {
                            r2Interpretation = ' (Moderate fit ⚠️)';
                            recommendations.push('Moderate performance. Try collecting more data or different features.');
                        } else if (r2Score >= 0.0) {
                            r2Interpretation = ' (Poor fit - needs improvement 📈)';
                            recommendations.push('Poor performance. Consider:');
                            recommendations.push('• Collecting more training data');
                            recommendations.push('• Adding more relevant features');
                            recommendations.push('• Checking data quality');
                        } else {
                            r2Interpretation = ' (Very poor fit - model worse than baseline 🔧)';
                            recommendations.push('Model is performing worse than a simple average. Consider:');
                            recommendations.push('• Checking for data quality issues');
                            recommendations.push('• Verifying target variable is correct');
                            recommendations.push('• Collecting more representative data');
                            recommendations.push('• Trying different target variables');

                            // Specific recommendations for terminal time
                            if (selectedTarget === 'terminal_time') {
                                recommendations.push('');
                                recommendations.push('Terminal Time specific suggestions:');
                                recommendations.push('• Terminal time is complex and depends on many external factors');
                                recommendations.push('• Consider using "pump_time" instead (usually more predictable)');
                                recommendations.push('• Add features like weather, port congestion, vessel delays');
                                recommendations.push('• Check for data quality issues (negative values, extreme outliers)');
                            }
                        }
                    }

                    // Interpret RMSE relative to target values
                    let rmseInterpretation = '';
                    if (rmse !== null) {
                        if (selectedTarget.includes('pump_time')) {
                            if (rmse < 30) {
                                rmseInterpretation = ' (Very accurate! ⭐)';
                            } else if (rmse < 60) {
                                rmseInterpretation = ' (Good accuracy 👍)';
                            } else if (rmse < 120) {
                                rmseInterpretation = ' (Moderate accuracy ⚠️)';
                            } else {
                                rmseInterpretation = ' (Low accuracy - needs improvement 📊)';
                            }
                        }
                    }

                    // Create recommendations HTML
                    let recommendationsHtml = '';
                    if (recommendations.length > 0) {
                        recommendationsHtml = '<div class="info">💡 Recommendations:</div>';
                        recommendations.forEach(rec => {
                            if (rec.startsWith('•')) {
                                recommendationsHtml += `<div class="info" style="margin-left: 20px;">${rec}</div>`;
                            } else {
                                recommendationsHtml += `<div class="info">${rec}</div>`;
                            }
                        });
                    }

                    showResults('trainResults', `
                        <div class="success">✅ Model trained successfully!</div>
                        <div class="info">🤖 Best model: ${result.best_model}</div>
                        <div class="info">📊 R² Score: ${r2Score !== null ? r2Score.toFixed(3) : 'N/A'}${r2Interpretation}</div>
                        <div class="info">📏 RMSE: ${rmse !== null ? rmse.toFixed(2) : 'N/A'} minutes${rmseInterpretation}</div>
                        ${mae !== null ? `<div class="info">📐 MAE: ${mae.toFixed(2)} minutes</div>` : ''}
                        ${mape !== null && mape < 1000 ? `<div class="info">📊 MAPE: ${mape.toFixed(1)}%</div>` : ''}
                        <div class="info">🎯 Target: ${selectedTarget.replace('_', ' ').toUpperCase()}</div>
                        <div class="info">📈 Training completed with ${result.model_summary.total_models_trained} algorithm(s)</div>
                        ${recommendationsHtml}
                    `);
                    completeStep(4);
                    enableStep(5);
                    document.getElementById('predictionForm').style.display = 'block';
                } else {
                    showResults('trainResults', `<div class="error">❌ Training failed: ${result.error}</div>`);
                }
            } catch (error) {
                showResults('trainResults', `<div class="error">❌ Training failed: ${error.message}</div>`);
            }
        });

        // Prediction
        document.getElementById('predictBtn').addEventListener('click', async () => {
            const inputData = {
                product_quantity: parseFloat(document.getElementById('productQuantity').value),
                vessel_type: document.getElementById('vesselType').value,
                product_type: document.getElementById('productType').value,
                operation_type: document.getElementById('operationType').value
            };

            try {
                const response = await fetch(`${API_BASE}/predict`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        input_data: inputData
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const prediction = result.prediction_result.prediction;
                    document.getElementById('predictionResult').innerHTML = `
                        <div class="prediction-result">
                            <h4>🎯 Prediction Result</h4>
                            <div class="prediction-value">${prediction.toFixed(1)} minutes</div>
                            <p>Predicted ${selectedTarget.replace('_', ' ')} for the given parameters</p>
                        </div>
                    `;
                    document.getElementById('predictionResult').style.display = 'block';
                    completeStep(5);
                } else {
                    showResults('predictionResult', `<div class="error">❌ Prediction failed: ${result.error}</div>`);
                }
            } catch (error) {
                showResults('predictionResult', `<div class="error">❌ Prediction failed: ${error.message}</div>`);
            }
        });

        function showLoading(elementId, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="info"><span class="loading"></span>${message}</div>`;
            element.style.display = 'block';
        }

        function showResults(elementId, content) {
            const element = document.getElementById(elementId);
            element.innerHTML = content;
            element.style.display = 'block';
        }

        function completeStep(stepNumber) {
            document.getElementById(`step${stepNumber}`).classList.add('completed');
        }

        function enableStep(stepNumber) {
            const step = document.getElementById(`step${stepNumber}`);
            step.classList.add('active');
            
            const buttons = {
                2: 'validateBtn',
                3: 'processBtn',
                4: 'trainBtn',
                5: 'predictBtn'
            };
            
            if (buttons[stepNumber]) {
                document.getElementById(buttons[stepNumber]).disabled = false;
            }
        }
    </script>
</body>
</html>
