{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Pumptimemodel\\\\frontend\\\\src\\\\components\\\\DataProcessing.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Button, Paper, Grid, FormControlLabel, Switch, Divider, List, ListItem, ListItemIcon, ListItemText, Chip, Alert } from '@mui/material';\nimport { Settings, ArrowBack, ArrowForward, CheckCircle, Info, Tune } from '@mui/icons-material';\nimport toast from 'react-hot-toast';\nimport { apiService } from '../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DataProcessing = ({\n  onNext,\n  onBack,\n  setLoading,\n  setError\n}) => {\n  _s();\n  const [processingOptions, setProcessingOptions] = useState({\n    cleaning_options: {\n      remove_duplicates: true,\n      handle_missing: 'impute',\n      handle_outliers: 'cap',\n      missing_threshold: 0.5\n    },\n    feature_engineering: {\n      apply_feature_engineering: true,\n      include_polynomial: false,\n      feature_selection_k: 20\n    }\n  });\n  const [processingResult, setProcessingResult] = useState(null);\n  const [processingSuccess, setProcessingSuccess] = useState(false);\n  const handleOptionChange = (category, option, value) => {\n    setProcessingOptions(prev => ({\n      ...prev,\n      [category]: {\n        ...prev[category],\n        [option]: value\n      }\n    }));\n  };\n  const processData = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const result = await apiService.processData(processingOptions);\n      setProcessingResult(result);\n      if (result.success) {\n        setProcessingSuccess(true);\n        toast.success('Data processed successfully!');\n      } else {\n        toast.error('Data processing failed. Please check the issues.');\n      }\n    } catch (error) {\n      setError(error.message);\n      toast.error('Processing failed: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const renderProcessingOptions = () => {\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: [/*#__PURE__*/_jsxDEV(Settings, {\n          sx: {\n            mr: 1,\n            verticalAlign: 'middle'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), \"Processing Options\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Data Cleaning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Switch, {\n              checked: processingOptions.cleaning_options.remove_duplicates,\n              onChange: e => handleOptionChange('cleaning_options', 'remove_duplicates', e.target.checked),\n              color: \"primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this),\n            label: \"Remove Duplicate Rows\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Switch, {\n              checked: processingOptions.cleaning_options.handle_outliers === 'cap',\n              onChange: e => handleOptionChange('cleaning_options', 'handle_outliers', e.target.checked ? 'cap' : 'none'),\n              color: \"primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 17\n            }, this),\n            label: \"Handle Outliers (Cap at 3 IQR)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Feature Engineering\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Switch, {\n              checked: processingOptions.feature_engineering.apply_feature_engineering,\n              onChange: e => handleOptionChange('feature_engineering', 'apply_feature_engineering', e.target.checked),\n              color: \"primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this),\n            label: \"Apply Feature Engineering\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Switch, {\n              checked: processingOptions.feature_engineering.include_polynomial,\n              onChange: e => handleOptionChange('feature_engineering', 'include_polynomial', e.target.checked),\n              color: \"primary\",\n              disabled: !processingOptions.feature_engineering.apply_feature_engineering\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 17\n            }, this),\n            label: \"Include Polynomial Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this);\n  };\n  const renderProcessingResult = () => {\n    var _processing_summary$e;\n    if (!processingResult) return null;\n    const {\n      processing_summary\n    } = processingResult;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Processing Results\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Data Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(List, {\n            dense: true,\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                children: /*#__PURE__*/_jsxDEV(Info, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Data Format\",\n                secondary: (processing_summary === null || processing_summary === void 0 ? void 0 : processing_summary.data_format) || 'standard',\n                primaryTypographyProps: {\n                  color: 'text.primary'\n                },\n                secondaryTypographyProps: {\n                  color: 'text.secondary'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                children: /*#__PURE__*/_jsxDEV(Info, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Original Shape\",\n                secondary: processing_summary !== null && processing_summary !== void 0 && processing_summary.original_shape ? `${processing_summary.original_shape[0]} rows × ${processing_summary.original_shape[1]} columns` : 'N/A',\n                primaryTypographyProps: {\n                  color: 'text.primary'\n                },\n                secondaryTypographyProps: {\n                  color: 'text.secondary'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                children: /*#__PURE__*/_jsxDEV(Info, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Final Shape\",\n                secondary: processing_summary !== null && processing_summary !== void 0 && processing_summary.final_shape ? `${processing_summary.final_shape[0]} rows × ${processing_summary.final_shape[1]} columns` : 'N/A',\n                primaryTypographyProps: {\n                  color: 'text.primary'\n                },\n                secondaryTypographyProps: {\n                  color: 'text.secondary'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Feature Engineering\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), processing_summary !== null && processing_summary !== void 0 && processing_summary.feature_engineering_applied ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"success\",\n            sx: {\n              mb: 2\n            },\n            children: [\"\\u2705 Feature engineering was successfully applied\", (processing_summary === null || processing_summary === void 0 ? void 0 : (_processing_summary$e = processing_summary.engineering_summary) === null || _processing_summary$e === void 0 ? void 0 : _processing_summary$e.features_created) && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                mt: 1\n              },\n              children: [\"\\uD83C\\uDFAF \", processing_summary.engineering_summary.features_created, \" new features created\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mb: 2\n            },\n            children: \"\\u2139\\uFE0F No feature engineering was applied\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this), (processing_summary === null || processing_summary === void 0 ? void 0 : processing_summary.engineering_summary) && /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Features Created:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this), \" \", processing_summary.engineering_summary.total_features_created || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this), processing_summary.engineering_summary.created_features && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexWrap: 'wrap',\n                gap: 0.5,\n                mt: 1\n              },\n              children: [processing_summary.engineering_summary.created_features.slice(0, 10).map((feature, index) => /*#__PURE__*/_jsxDEV(Chip, {\n                label: feature,\n                size: \"small\",\n                variant: \"outlined\",\n                color: \"primary\"\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 23\n              }, this)), processing_summary.engineering_summary.created_features.length > 10 && /*#__PURE__*/_jsxDEV(Chip, {\n                label: `+${processing_summary.engineering_summary.created_features.length - 10} more`,\n                size: \"small\",\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), (processing_summary === null || processing_summary === void 0 ? void 0 : processing_summary.columns_after_processing) && /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Available Columns After Processing\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: 0.5\n          },\n          children: [processing_summary.columns_after_processing.slice(0, 15).map((column, index) => /*#__PURE__*/_jsxDEV(Chip, {\n            label: column,\n            size: \"small\",\n            variant: \"outlined\"\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 17\n          }, this)), processing_summary.columns_after_processing.length > 15 && /*#__PURE__*/_jsxDEV(Chip, {\n            label: `+${processing_summary.columns_after_processing.length - 15} more`,\n            size: \"small\",\n            variant: \"outlined\",\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 11\n      }, this), (processing_summary === null || processing_summary === void 0 ? void 0 : processing_summary.available_targets) && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Available Target Variables\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: 0.5\n          },\n          children: processing_summary.available_targets.map((target, index) => /*#__PURE__*/_jsxDEV(Chip, {\n            label: target,\n            size: \"small\",\n            color: \"secondary\"\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Process & Clean Data\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      paragraph: true,\n      children: \"Process and clean your data to prepare it for model training. This step handles missing values, outliers, and creates engineered features.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this), renderProcessingOptions(), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      color: \"primary\",\n      onClick: processData,\n      startIcon: /*#__PURE__*/_jsxDEV(Tune, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 20\n      }, this),\n      sx: {\n        mb: 2\n      },\n      children: \"Process Data\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this), processingResult && renderProcessingResult(), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        onClick: onBack,\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 22\n        }, this),\n        children: \"Back\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: onNext,\n        endIcon: /*#__PURE__*/_jsxDEV(ArrowForward, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 20\n        }, this),\n        disabled: !processingSuccess,\n        children: \"Continue to Feature Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 317,\n    columnNumber: 5\n  }, this);\n};\n_s(DataProcessing, \"9uPwf6jzoOBVe064RP1VOfuhGXk=\");\n_c = DataProcessing;\nexport default DataProcessing;\nvar _c;\n$RefreshReg$(_c, \"DataProcessing\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "FormControlLabel", "Switch", "Divider", "List", "ListItem", "ListItemIcon", "ListItemText", "Chip", "<PERSON><PERSON>", "Settings", "ArrowBack", "ArrowForward", "CheckCircle", "Info", "<PERSON><PERSON>", "toast", "apiService", "jsxDEV", "_jsxDEV", "DataProcessing", "onNext", "onBack", "setLoading", "setError", "_s", "processingOptions", "setProcessingOptions", "cleaning_options", "remove_duplicates", "handle_missing", "handle_outliers", "missing_threshold", "feature_engineering", "apply_feature_engineering", "include_polynomial", "feature_selection_k", "processingResult", "setProcessingResult", "processingSuccess", "setProcessingSuccess", "handleOptionChange", "category", "option", "value", "prev", "processData", "result", "success", "error", "message", "renderProcessingOptions", "sx", "p", "mb", "children", "variant", "gutterBottom", "mr", "verticalAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "item", "xs", "md", "control", "checked", "onChange", "e", "target", "color", "label", "disabled", "renderProcessingResult", "_processing_summary$e", "processing_summary", "mt", "dense", "primary", "secondary", "data_format", "primaryTypographyProps", "secondaryTypographyProps", "original_shape", "final_shape", "feature_engineering_applied", "severity", "engineering_summary", "features_created", "total_features_created", "created_features", "display", "flexWrap", "gap", "slice", "map", "feature", "index", "size", "length", "my", "columns_after_processing", "column", "available_targets", "paragraph", "onClick", "startIcon", "justifyContent", "endIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Pumptimemodel/frontend/src/components/DataProcessing.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Button,\r\n  Paper,\r\n  Grid,\r\n  FormControlLabel,\r\n  Switch,\r\n  Divider,\r\n  List,\r\n  ListItem,\r\n  ListItemIcon,\r\n  ListItemText,\r\n  Chip,\r\n  Alert\r\n} from '@mui/material';\r\nimport {\r\n  Settings,\r\n  ArrowBack,\r\n  <PERSON>Forward,\r\n  CheckCircle,\r\n  Info,\r\n  Tune\r\n} from '@mui/icons-material';\r\nimport toast from 'react-hot-toast';\r\n\r\nimport { apiService } from '../services/apiService';\r\n\r\nconst DataProcessing = ({ onNext, onBack, setLoading, setError }) => {\r\n  const [processingOptions, setProcessingOptions] = useState({\r\n    cleaning_options: {\r\n      remove_duplicates: true,\r\n      handle_missing: 'impute',\r\n      handle_outliers: 'cap',\r\n      missing_threshold: 0.5\r\n    },\r\n    feature_engineering: {\r\n      apply_feature_engineering: true,\r\n      include_polynomial: false,\r\n      feature_selection_k: 20\r\n    }\r\n  });\r\n  \r\n  const [processingResult, setProcessingResult] = useState(null);\r\n  const [processingSuccess, setProcessingSuccess] = useState(false);\r\n\r\n  const handleOptionChange = (category, option, value) => {\r\n    setProcessingOptions(prev => ({\r\n      ...prev,\r\n      [category]: {\r\n        ...prev[category],\r\n        [option]: value\r\n      }\r\n    }));\r\n  };\r\n\r\n  const processData = async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const result = await apiService.processData(processingOptions);\r\n      setProcessingResult(result);\r\n      \r\n      if (result.success) {\r\n        setProcessingSuccess(true);\r\n        toast.success('Data processed successfully!');\r\n      } else {\r\n        toast.error('Data processing failed. Please check the issues.');\r\n      }\r\n    } catch (error) {\r\n      setError(error.message);\r\n      toast.error('Processing failed: ' + error.message);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const renderProcessingOptions = () => {\r\n    return (\r\n      <Paper sx={{ p: 3, mb: 3 }}>\r\n        <Typography variant=\"h6\" gutterBottom>\r\n          <Settings sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n          Processing Options\r\n        </Typography>\r\n        \r\n        <Grid container spacing={3}>\r\n          {/* Data Cleaning Options */}\r\n          <Grid item xs={12} md={6}>\r\n            <Typography variant=\"subtitle1\" gutterBottom>\r\n              Data Cleaning\r\n            </Typography>\r\n            \r\n            <FormControlLabel\r\n              control={\r\n                <Switch\r\n                  checked={processingOptions.cleaning_options.remove_duplicates}\r\n                  onChange={(e) => handleOptionChange('cleaning_options', 'remove_duplicates', e.target.checked)}\r\n                  color=\"primary\"\r\n                />\r\n              }\r\n              label=\"Remove Duplicate Rows\"\r\n            />\r\n            \r\n            <FormControlLabel\r\n              control={\r\n                <Switch\r\n                  checked={processingOptions.cleaning_options.handle_outliers === 'cap'}\r\n                  onChange={(e) => handleOptionChange(\r\n                    'cleaning_options', \r\n                    'handle_outliers', \r\n                    e.target.checked ? 'cap' : 'none'\r\n                  )}\r\n                  color=\"primary\"\r\n                />\r\n              }\r\n              label=\"Handle Outliers (Cap at 3 IQR)\"\r\n            />\r\n          </Grid>\r\n          \r\n          {/* Feature Engineering Options */}\r\n          <Grid item xs={12} md={6}>\r\n            <Typography variant=\"subtitle1\" gutterBottom>\r\n              Feature Engineering\r\n            </Typography>\r\n            \r\n            <FormControlLabel\r\n              control={\r\n                <Switch\r\n                  checked={processingOptions.feature_engineering.apply_feature_engineering}\r\n                  onChange={(e) => handleOptionChange('feature_engineering', 'apply_feature_engineering', e.target.checked)}\r\n                  color=\"primary\"\r\n                />\r\n              }\r\n              label=\"Apply Feature Engineering\"\r\n            />\r\n            \r\n            <FormControlLabel\r\n              control={\r\n                <Switch\r\n                  checked={processingOptions.feature_engineering.include_polynomial}\r\n                  onChange={(e) => handleOptionChange('feature_engineering', 'include_polynomial', e.target.checked)}\r\n                  color=\"primary\"\r\n                  disabled={!processingOptions.feature_engineering.apply_feature_engineering}\r\n                />\r\n              }\r\n              label=\"Include Polynomial Features\"\r\n            />\r\n          </Grid>\r\n        </Grid>\r\n      </Paper>\r\n    );\r\n  };\r\n\r\n  const renderProcessingResult = () => {\r\n    if (!processingResult) return null;\r\n\r\n    const { processing_summary } = processingResult;\r\n    \r\n    return (\r\n      <Paper sx={{ p: 3, mt: 3 }}>\r\n        <Typography variant=\"h6\" gutterBottom>\r\n          Processing Results\r\n        </Typography>\r\n        \r\n        <Grid container spacing={3}>\r\n          {/* Data Format */}\r\n          <Grid item xs={12} md={6}>\r\n            <Typography variant=\"subtitle1\" gutterBottom>\r\n              Data Information\r\n            </Typography>\r\n            <List dense>\r\n              <ListItem>\r\n                <ListItemIcon>\r\n                  <Info color=\"primary\" />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary=\"Data Format\"\r\n                  secondary={processing_summary?.data_format || 'standard'}\r\n                  primaryTypographyProps={{ color: 'text.primary' }}\r\n                  secondaryTypographyProps={{ color: 'text.secondary' }}\r\n                />\r\n              </ListItem>\r\n              <ListItem>\r\n                <ListItemIcon>\r\n                  <Info color=\"primary\" />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary=\"Original Shape\"\r\n                  secondary={processing_summary?.original_shape ?\r\n                    `${processing_summary.original_shape[0]} rows × ${processing_summary.original_shape[1]} columns` :\r\n                    'N/A'}\r\n                  primaryTypographyProps={{ color: 'text.primary' }}\r\n                  secondaryTypographyProps={{ color: 'text.secondary' }}\r\n                />\r\n              </ListItem>\r\n              <ListItem>\r\n                <ListItemIcon>\r\n                  <Info color=\"primary\" />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary=\"Final Shape\"\r\n                  secondary={processing_summary?.final_shape ?\r\n                    `${processing_summary.final_shape[0]} rows × ${processing_summary.final_shape[1]} columns` :\r\n                    'N/A'}\r\n                  primaryTypographyProps={{ color: 'text.primary' }}\r\n                  secondaryTypographyProps={{ color: 'text.secondary' }}\r\n                />\r\n              </ListItem>\r\n            </List>\r\n          </Grid>\r\n          \r\n          {/* Feature Engineering */}\r\n          <Grid item xs={12} md={6}>\r\n            <Typography variant=\"subtitle1\" gutterBottom>\r\n              Feature Engineering\r\n            </Typography>\r\n            {processing_summary?.feature_engineering_applied ? (\r\n              <Alert severity=\"success\" sx={{ mb: 2 }}>\r\n                ✅ Feature engineering was successfully applied\r\n                {processing_summary?.engineering_summary?.features_created && (\r\n                  <Typography variant=\"body2\" sx={{ mt: 1 }}>\r\n                    🎯 {processing_summary.engineering_summary.features_created} new features created\r\n                  </Typography>\r\n                )}\r\n              </Alert>\r\n            ) : (\r\n              <Alert severity=\"info\" sx={{ mb: 2 }}>\r\n                ℹ️ No feature engineering was applied\r\n              </Alert>\r\n            )}\r\n\r\n            {processing_summary?.engineering_summary && (\r\n              <Box>\r\n                <Typography variant=\"body2\" gutterBottom>\r\n                  <strong>Features Created:</strong> {processing_summary.engineering_summary.total_features_created || 0}\r\n                </Typography>\r\n                \r\n                {processing_summary.engineering_summary.created_features && (\r\n                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>\r\n                    {processing_summary.engineering_summary.created_features.slice(0, 10).map((feature, index) => (\r\n                      <Chip \r\n                        key={index} \r\n                        label={feature} \r\n                        size=\"small\" \r\n                        variant=\"outlined\"\r\n                        color=\"primary\"\r\n                      />\r\n                    ))}\r\n                    {processing_summary.engineering_summary.created_features.length > 10 && (\r\n                      <Chip \r\n                        label={`+${processing_summary.engineering_summary.created_features.length - 10} more`} \r\n                        size=\"small\" \r\n                        variant=\"outlined\"\r\n                      />\r\n                    )}\r\n                  </Box>\r\n                )}\r\n              </Box>\r\n            )}\r\n          </Grid>\r\n        </Grid>\r\n        \r\n        <Divider sx={{ my: 2 }} />\r\n        \r\n        {/* Available Columns */}\r\n        {processing_summary?.columns_after_processing && (\r\n          <Box>\r\n            <Typography variant=\"subtitle1\" gutterBottom>\r\n              Available Columns After Processing\r\n            </Typography>\r\n            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>\r\n              {processing_summary.columns_after_processing.slice(0, 15).map((column, index) => (\r\n                <Chip \r\n                  key={index} \r\n                  label={column} \r\n                  size=\"small\" \r\n                  variant=\"outlined\"\r\n                />\r\n              ))}\r\n              {processing_summary.columns_after_processing.length > 15 && (\r\n                <Chip \r\n                  label={`+${processing_summary.columns_after_processing.length - 15} more`} \r\n                  size=\"small\" \r\n                  variant=\"outlined\"\r\n                  color=\"primary\"\r\n                />\r\n              )}\r\n            </Box>\r\n          </Box>\r\n        )}\r\n        \r\n        {/* Available Targets */}\r\n        {processing_summary?.available_targets && (\r\n          <Box sx={{ mt: 2 }}>\r\n            <Typography variant=\"subtitle1\" gutterBottom>\r\n              Available Target Variables\r\n            </Typography>\r\n            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>\r\n              {processing_summary.available_targets.map((target, index) => (\r\n                <Chip \r\n                  key={index} \r\n                  label={target} \r\n                  size=\"small\" \r\n                  color=\"secondary\"\r\n                />\r\n              ))}\r\n            </Box>\r\n          </Box>\r\n        )}\r\n      </Paper>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Box>\r\n      <Typography variant=\"h4\" gutterBottom>\r\n        Process & Clean Data\r\n      </Typography>\r\n      <Typography variant=\"body1\" color=\"text.secondary\" paragraph>\r\n        Process and clean your data to prepare it for model training.\r\n        This step handles missing values, outliers, and creates engineered features.\r\n      </Typography>\r\n      \r\n      {renderProcessingOptions()}\r\n      \r\n      <Button \r\n        variant=\"contained\" \r\n        color=\"primary\" \r\n        onClick={processData}\r\n        startIcon={<Tune />}\r\n        sx={{ mb: 2 }}\r\n      >\r\n        Process Data\r\n      </Button>\r\n      \r\n      {processingResult && renderProcessingResult()}\r\n      \r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>\r\n        <Button \r\n          variant=\"outlined\" \r\n          onClick={onBack}\r\n          startIcon={<ArrowBack />}\r\n        >\r\n          Back\r\n        </Button>\r\n        \r\n        <Button \r\n          variant=\"contained\" \r\n          color=\"primary\" \r\n          onClick={onNext}\r\n          endIcon={<ArrowForward />}\r\n          disabled={!processingSuccess}\r\n        >\r\n          Continue to Feature Management\r\n        </Button>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default DataProcessing;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,gBAAgB,EAChBC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,IAAI,EACJC,KAAK,QACA,eAAe;AACtB,SACEC,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,WAAW,EACXC,IAAI,EACJC,IAAI,QACC,qBAAqB;AAC5B,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,UAAU,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,cAAc,GAAGA,CAAC;EAAEC,MAAM;EAAEC,MAAM;EAAEC,UAAU;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACnE,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CAAC;IACzDiC,gBAAgB,EAAE;MAChBC,iBAAiB,EAAE,IAAI;MACvBC,cAAc,EAAE,QAAQ;MACxBC,eAAe,EAAE,KAAK;MACtBC,iBAAiB,EAAE;IACrB,CAAC;IACDC,mBAAmB,EAAE;MACnBC,yBAAyB,EAAE,IAAI;MAC/BC,kBAAkB,EAAE,KAAK;MACzBC,mBAAmB,EAAE;IACvB;EACF,CAAC,CAAC;EAEF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC4C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAM8C,kBAAkB,GAAGA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,KAAK;IACtDjB,oBAAoB,CAACkB,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACP,CAACH,QAAQ,GAAG;QACV,GAAGG,IAAI,CAACH,QAAQ,CAAC;QACjB,CAACC,MAAM,GAAGC;MACZ;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BvB,UAAU,CAAC,IAAI,CAAC;IAChBC,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMuB,MAAM,GAAG,MAAM9B,UAAU,CAAC6B,WAAW,CAACpB,iBAAiB,CAAC;MAC9DY,mBAAmB,CAACS,MAAM,CAAC;MAE3B,IAAIA,MAAM,CAACC,OAAO,EAAE;QAClBR,oBAAoB,CAAC,IAAI,CAAC;QAC1BxB,KAAK,CAACgC,OAAO,CAAC,8BAA8B,CAAC;MAC/C,CAAC,MAAM;QACLhC,KAAK,CAACiC,KAAK,CAAC,kDAAkD,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdzB,QAAQ,CAACyB,KAAK,CAACC,OAAO,CAAC;MACvBlC,KAAK,CAACiC,KAAK,CAAC,qBAAqB,GAAGA,KAAK,CAACC,OAAO,CAAC;IACpD,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4B,uBAAuB,GAAGA,CAAA,KAAM;IACpC,oBACEhC,OAAA,CAACpB,KAAK;MAACqD,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzBpC,OAAA,CAACtB,UAAU;QAAC2D,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,gBACnCpC,OAAA,CAACT,QAAQ;UAAC0C,EAAE,EAAE;YAAEM,EAAE,EAAE,CAAC;YAAEC,aAAa,EAAE;UAAS;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,sBAEtD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb5C,OAAA,CAACnB,IAAI;QAACgE,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAV,QAAA,gBAEzBpC,OAAA,CAACnB,IAAI;UAACkE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAb,QAAA,gBACvBpC,OAAA,CAACtB,UAAU;YAAC2D,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAF,QAAA,EAAC;UAE7C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb5C,OAAA,CAAClB,gBAAgB;YACfoE,OAAO,eACLlD,OAAA,CAACjB,MAAM;cACLoE,OAAO,EAAE5C,iBAAiB,CAACE,gBAAgB,CAACC,iBAAkB;cAC9D0C,QAAQ,EAAGC,CAAC,IAAK/B,kBAAkB,CAAC,kBAAkB,EAAE,mBAAmB,EAAE+B,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;cAC/FI,KAAK,EAAC;YAAS;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CACF;YACDY,KAAK,EAAC;UAAuB;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eAEF5C,OAAA,CAAClB,gBAAgB;YACfoE,OAAO,eACLlD,OAAA,CAACjB,MAAM;cACLoE,OAAO,EAAE5C,iBAAiB,CAACE,gBAAgB,CAACG,eAAe,KAAK,KAAM;cACtEwC,QAAQ,EAAGC,CAAC,IAAK/B,kBAAkB,CACjC,kBAAkB,EAClB,iBAAiB,EACjB+B,CAAC,CAACC,MAAM,CAACH,OAAO,GAAG,KAAK,GAAG,MAC7B,CAAE;cACFI,KAAK,EAAC;YAAS;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CACF;YACDY,KAAK,EAAC;UAAgC;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGP5C,OAAA,CAACnB,IAAI;UAACkE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAb,QAAA,gBACvBpC,OAAA,CAACtB,UAAU;YAAC2D,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAF,QAAA,EAAC;UAE7C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb5C,OAAA,CAAClB,gBAAgB;YACfoE,OAAO,eACLlD,OAAA,CAACjB,MAAM;cACLoE,OAAO,EAAE5C,iBAAiB,CAACO,mBAAmB,CAACC,yBAA0B;cACzEqC,QAAQ,EAAGC,CAAC,IAAK/B,kBAAkB,CAAC,qBAAqB,EAAE,2BAA2B,EAAE+B,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;cAC1GI,KAAK,EAAC;YAAS;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CACF;YACDY,KAAK,EAAC;UAA2B;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eAEF5C,OAAA,CAAClB,gBAAgB;YACfoE,OAAO,eACLlD,OAAA,CAACjB,MAAM;cACLoE,OAAO,EAAE5C,iBAAiB,CAACO,mBAAmB,CAACE,kBAAmB;cAClEoC,QAAQ,EAAGC,CAAC,IAAK/B,kBAAkB,CAAC,qBAAqB,EAAE,oBAAoB,EAAE+B,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;cACnGI,KAAK,EAAC,SAAS;cACfE,QAAQ,EAAE,CAAClD,iBAAiB,CAACO,mBAAmB,CAACC;YAA0B;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CACF;YACDY,KAAK,EAAC;UAA6B;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;EAED,MAAMc,sBAAsB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IACnC,IAAI,CAACzC,gBAAgB,EAAE,OAAO,IAAI;IAElC,MAAM;MAAE0C;IAAmB,CAAC,GAAG1C,gBAAgB;IAE/C,oBACElB,OAAA,CAACpB,KAAK;MAACqD,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAE2B,EAAE,EAAE;MAAE,CAAE;MAAAzB,QAAA,gBACzBpC,OAAA,CAACtB,UAAU;QAAC2D,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb5C,OAAA,CAACnB,IAAI;QAACgE,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAV,QAAA,gBAEzBpC,OAAA,CAACnB,IAAI;UAACkE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAb,QAAA,gBACvBpC,OAAA,CAACtB,UAAU;YAAC2D,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAF,QAAA,EAAC;UAE7C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5C,OAAA,CAACf,IAAI;YAAC6E,KAAK;YAAA1B,QAAA,gBACTpC,OAAA,CAACd,QAAQ;cAAAkD,QAAA,gBACPpC,OAAA,CAACb,YAAY;gBAAAiD,QAAA,eACXpC,OAAA,CAACL,IAAI;kBAAC4D,KAAK,EAAC;gBAAS;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACf5C,OAAA,CAACZ,YAAY;gBACX2E,OAAO,EAAC,aAAa;gBACrBC,SAAS,EAAE,CAAAJ,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEK,WAAW,KAAI,UAAW;gBACzDC,sBAAsB,EAAE;kBAAEX,KAAK,EAAE;gBAAe,CAAE;gBAClDY,wBAAwB,EAAE;kBAAEZ,KAAK,EAAE;gBAAiB;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACX5C,OAAA,CAACd,QAAQ;cAAAkD,QAAA,gBACPpC,OAAA,CAACb,YAAY;gBAAAiD,QAAA,eACXpC,OAAA,CAACL,IAAI;kBAAC4D,KAAK,EAAC;gBAAS;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACf5C,OAAA,CAACZ,YAAY;gBACX2E,OAAO,EAAC,gBAAgB;gBACxBC,SAAS,EAAEJ,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAEQ,cAAc,GAC3C,GAAGR,kBAAkB,CAACQ,cAAc,CAAC,CAAC,CAAC,WAAWR,kBAAkB,CAACQ,cAAc,CAAC,CAAC,CAAC,UAAU,GAChG,KAAM;gBACRF,sBAAsB,EAAE;kBAAEX,KAAK,EAAE;gBAAe,CAAE;gBAClDY,wBAAwB,EAAE;kBAAEZ,KAAK,EAAE;gBAAiB;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACX5C,OAAA,CAACd,QAAQ;cAAAkD,QAAA,gBACPpC,OAAA,CAACb,YAAY;gBAAAiD,QAAA,eACXpC,OAAA,CAACL,IAAI;kBAAC4D,KAAK,EAAC;gBAAS;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACf5C,OAAA,CAACZ,YAAY;gBACX2E,OAAO,EAAC,aAAa;gBACrBC,SAAS,EAAEJ,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAES,WAAW,GACxC,GAAGT,kBAAkB,CAACS,WAAW,CAAC,CAAC,CAAC,WAAWT,kBAAkB,CAACS,WAAW,CAAC,CAAC,CAAC,UAAU,GAC1F,KAAM;gBACRH,sBAAsB,EAAE;kBAAEX,KAAK,EAAE;gBAAe,CAAE;gBAClDY,wBAAwB,EAAE;kBAAEZ,KAAK,EAAE;gBAAiB;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP5C,OAAA,CAACnB,IAAI;UAACkE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAb,QAAA,gBACvBpC,OAAA,CAACtB,UAAU;YAAC2D,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAF,QAAA,EAAC;UAE7C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZgB,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAEU,2BAA2B,gBAC9CtE,OAAA,CAACV,KAAK;YAACiF,QAAQ,EAAC,SAAS;YAACtC,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAC,QAAA,GAAC,qDAEvC,EAAC,CAAAwB,kBAAkB,aAAlBA,kBAAkB,wBAAAD,qBAAA,GAAlBC,kBAAkB,CAAEY,mBAAmB,cAAAb,qBAAA,uBAAvCA,qBAAA,CAAyCc,gBAAgB,kBACxDzE,OAAA,CAACtB,UAAU;cAAC2D,OAAO,EAAC,OAAO;cAACJ,EAAE,EAAE;gBAAE4B,EAAE,EAAE;cAAE,CAAE;cAAAzB,QAAA,GAAC,eACtC,EAACwB,kBAAkB,CAACY,mBAAmB,CAACC,gBAAgB,EAAC,uBAC9D;YAAA;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,gBAER5C,OAAA,CAACV,KAAK;YAACiF,QAAQ,EAAC,MAAM;YAACtC,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAC,QAAA,EAAC;UAEtC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR,EAEA,CAAAgB,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEY,mBAAmB,kBACtCxE,OAAA,CAACvB,GAAG;YAAA2D,QAAA,gBACFpC,OAAA,CAACtB,UAAU;cAAC2D,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAF,QAAA,gBACtCpC,OAAA;gBAAAoC,QAAA,EAAQ;cAAiB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACgB,kBAAkB,CAACY,mBAAmB,CAACE,sBAAsB,IAAI,CAAC;YAAA;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC,EAEZgB,kBAAkB,CAACY,mBAAmB,CAACG,gBAAgB,iBACtD3E,OAAA,CAACvB,GAAG;cAACwD,EAAE,EAAE;gBAAE2C,OAAO,EAAE,MAAM;gBAAEC,QAAQ,EAAE,MAAM;gBAAEC,GAAG,EAAE,GAAG;gBAAEjB,EAAE,EAAE;cAAE,CAAE;cAAAzB,QAAA,GAC7DwB,kBAAkB,CAACY,mBAAmB,CAACG,gBAAgB,CAACI,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACvFlF,OAAA,CAACX,IAAI;gBAEHmE,KAAK,EAAEyB,OAAQ;gBACfE,IAAI,EAAC,OAAO;gBACZ9C,OAAO,EAAC,UAAU;gBAClBkB,KAAK,EAAC;cAAS,GAJV2B,KAAK;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKX,CACF,CAAC,EACDgB,kBAAkB,CAACY,mBAAmB,CAACG,gBAAgB,CAACS,MAAM,GAAG,EAAE,iBAClEpF,OAAA,CAACX,IAAI;gBACHmE,KAAK,EAAE,IAAII,kBAAkB,CAACY,mBAAmB,CAACG,gBAAgB,CAACS,MAAM,GAAG,EAAE,OAAQ;gBACtFD,IAAI,EAAC,OAAO;gBACZ9C,OAAO,EAAC;cAAU;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP5C,OAAA,CAAChB,OAAO;QAACiD,EAAE,EAAE;UAAEoD,EAAE,EAAE;QAAE;MAAE;QAAA5C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGzB,CAAAgB,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAE0B,wBAAwB,kBAC3CtF,OAAA,CAACvB,GAAG;QAAA2D,QAAA,gBACFpC,OAAA,CAACtB,UAAU;UAAC2D,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5C,OAAA,CAACvB,GAAG;UAACwD,EAAE,EAAE;YAAE2C,OAAO,EAAE,MAAM;YAAEC,QAAQ,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAI,CAAE;UAAA1C,QAAA,GACtDwB,kBAAkB,CAAC0B,wBAAwB,CAACP,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,GAAG,CAAC,CAACO,MAAM,EAAEL,KAAK,kBAC1ElF,OAAA,CAACX,IAAI;YAEHmE,KAAK,EAAE+B,MAAO;YACdJ,IAAI,EAAC,OAAO;YACZ9C,OAAO,EAAC;UAAU,GAHb6C,KAAK;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIX,CACF,CAAC,EACDgB,kBAAkB,CAAC0B,wBAAwB,CAACF,MAAM,GAAG,EAAE,iBACtDpF,OAAA,CAACX,IAAI;YACHmE,KAAK,EAAE,IAAII,kBAAkB,CAAC0B,wBAAwB,CAACF,MAAM,GAAG,EAAE,OAAQ;YAC1ED,IAAI,EAAC,OAAO;YACZ9C,OAAO,EAAC,UAAU;YAClBkB,KAAK,EAAC;UAAS;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAAAgB,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAE4B,iBAAiB,kBACpCxF,OAAA,CAACvB,GAAG;QAACwD,EAAE,EAAE;UAAE4B,EAAE,EAAE;QAAE,CAAE;QAAAzB,QAAA,gBACjBpC,OAAA,CAACtB,UAAU;UAAC2D,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5C,OAAA,CAACvB,GAAG;UAACwD,EAAE,EAAE;YAAE2C,OAAO,EAAE,MAAM;YAAEC,QAAQ,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAI,CAAE;UAAA1C,QAAA,EACtDwB,kBAAkB,CAAC4B,iBAAiB,CAACR,GAAG,CAAC,CAAC1B,MAAM,EAAE4B,KAAK,kBACtDlF,OAAA,CAACX,IAAI;YAEHmE,KAAK,EAAEF,MAAO;YACd6B,IAAI,EAAC,OAAO;YACZ5B,KAAK,EAAC;UAAW,GAHZ2B,KAAK;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIX,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEZ,CAAC;EAED,oBACE5C,OAAA,CAACvB,GAAG;IAAA2D,QAAA,gBACFpC,OAAA,CAACtB,UAAU;MAAC2D,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACb5C,OAAA,CAACtB,UAAU;MAAC2D,OAAO,EAAC,OAAO;MAACkB,KAAK,EAAC,gBAAgB;MAACkC,SAAS;MAAArD,QAAA,EAAC;IAG7D;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZZ,uBAAuB,CAAC,CAAC,eAE1BhC,OAAA,CAACrB,MAAM;MACL0D,OAAO,EAAC,WAAW;MACnBkB,KAAK,EAAC,SAAS;MACfmC,OAAO,EAAE/D,WAAY;MACrBgE,SAAS,eAAE3F,OAAA,CAACJ,IAAI;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACpBX,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,EACf;IAED;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAER1B,gBAAgB,IAAIwC,sBAAsB,CAAC,CAAC,eAE7C1D,OAAA,CAACvB,GAAG;MAACwD,EAAE,EAAE;QAAE2C,OAAO,EAAE,MAAM;QAAEgB,cAAc,EAAE,eAAe;QAAE/B,EAAE,EAAE;MAAE,CAAE;MAAAzB,QAAA,gBACnEpC,OAAA,CAACrB,MAAM;QACL0D,OAAO,EAAC,UAAU;QAClBqD,OAAO,EAAEvF,MAAO;QAChBwF,SAAS,eAAE3F,OAAA,CAACR,SAAS;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAR,QAAA,EAC1B;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET5C,OAAA,CAACrB,MAAM;QACL0D,OAAO,EAAC,WAAW;QACnBkB,KAAK,EAAC,SAAS;QACfmC,OAAO,EAAExF,MAAO;QAChB2F,OAAO,eAAE7F,OAAA,CAACP,YAAY;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC1Ba,QAAQ,EAAE,CAACrC,iBAAkB;QAAAgB,QAAA,EAC9B;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtC,EAAA,CA3UIL,cAAc;AAAA6F,EAAA,GAAd7F,cAAc;AA6UpB,eAAeA,cAAc;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}