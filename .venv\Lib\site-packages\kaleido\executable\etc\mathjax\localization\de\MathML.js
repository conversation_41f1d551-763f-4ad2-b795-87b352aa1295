/*************************************************************
 *
 *  MathJax/localization/de/MathML.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("de","MathML",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          BadMglyph: "ung\u00FCltige mglyph: %1",
          BadMglyphFont: "Ung\u00FCltige Schriftart: %1",
          MathPlayer: "MathJax konnte MathPlayer nicht einrichten.\n\nFalls MathPlayer nicht installiert ist, musst du ihn zuerst installieren.\nAnderenfalls verhindern deine Sicherheitseinstellungen, dass\nActiveX-Steuerelemente ausgef\u00FChrt werden k\u00F6nnen. Verwende die\nInternetoptionen unter dem Werkzeugmen\u00FC und w\u00E4hle die Registerkarte\n\u201ESicherheit\u201C. Klicke dann auf \u201EStufe anpassen\u201C. \u00DCberpr\u00FCfe, ob die\nEinstellungen f\u00FCr \u201EActiveX-Steuerelemente ausf\u00FChren\u201C und \u201EBin\u00E4r- und\nSkriptverhalten\u201C aktiviert sind.\n\nDerzeit wirst du Fehlermeldungen sehen anstatt gesetzte Inhalte.",
          CantCreateXMLParser: "MathJax konnte keinen XML-Parser f\u00FCr MathML erstellen.\n\u00DCberpr\u00FCfe, ob die Sicherheitseinstellung\n\u201EActiveX-Steuerelemente ausf\u00FChren, die f\u00FCr Skripting\nsicher sind\u201C aktiviert ist (verwende zur \u00DCberpr\u00FCfung\ndie Internetoptionen im Werkzeugmen\u00FC, w\u00E4hle die\nRegisterkarte \u201ESicherheit\u201C und klicke auf \u201EStufe\nanpassen\u201C).\n\nMathML-Gleichungen k\u00F6nnen von MathJax nicht\nverarbeitet werden.",
          UnknownNodeType: "Unbekannter Knotentyp: %1",
          UnexpectedTextNode: "Unerwarteter Textknoten: %1",
          ErrorParsingMathML: "Fehler beim Verarbeiten von MathML",
          ParsingError: "Fehler beim Parsen von MathML: %1",
          MathMLSingleElement: "MathML muss von einem einzelnen Element gebildet werden",
          MathMLRootElement: "MathML muss von einem \u003Cmath\u003E-Element gebildet werden, nicht %1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/de/MathML.js");
