/*************************************************************
 *
 *  MathJax/localization/es/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("es","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "Ayuda de MathJax",
          MathJax: "*MathJax* es una biblioteca en JavaScript que permite a los autores de p\u00E1ginas web incrustar notaci\u00F3n matem\u00E1tica. Como lector, no necesitas hacer nada para que eso suceda.",
          Browsers: "*Navegadores*: MathJax funciona con todos los navegadores modernos, incluyendo IE6+, Firefox 3+, Chrome 0.2+, Safari 2+, Opera 9.6+ y la mayor\u00EDa de los navegadores para m\u00F3vil.",
          Menu: "*Men\u00FA Math*: MathJax a\u00F1ade un men\u00FA contextual para las ecuaciones. Haz clic derecho o Ctrl-clic en cualquier notaci\u00F3n matem\u00E1tica para acceder al men\u00FA.",
          ShowMath: "*Mostrar la matem\u00E1tica como* permite ver el formato del c\u00F3digo fuente de la f\u00F3rmula para copiar y pegar (como MathML o en su formato original).",
          Settings: "*Ajustes* te da el control sobre las funciones de MathJax, tales como el tama\u00F1o de las notaciones matem\u00E1ticas y el mecanismo que se utiliza para mostrar las ecuaciones.",
          Language: "*Idioma* permite seleccionar el idioma utilizado por MathJax para sus men\u00FAs y mensajes de advertencia.",
          Zoom: "*Zoom*: Si est\u00E1s teniendo dificultad para leer una ecuaci\u00F3n, MathJax puede ampliarla para ayudarte a verla mejor.",
          Accessibilty: "*Accesibilidad*: MathJax funcionar\u00E1 autom\u00E1ticamente con los lectores de pantalla para hacer accesible a las personas con discapacidad visual.",
          Fonts: "*Tipos de letra*: MathJax utiliza determinados tipos de letra matem\u00E1ticas si est\u00E1n instalados en tu ordenador. De lo contrario, utilizar\u00E1 tipos de letra en web. Aunque no es necesario, los tipos de letra instalados localmente aumentar\u00E1 la velocidad de composici\u00F3n tipogr\u00E1fica. Sugerimos instalar los tipos de letra [STIX](%1).",
          CloseDialog: "Cerrar el di\u00E1logo de ayuda"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/es/HelpDialog.js");
