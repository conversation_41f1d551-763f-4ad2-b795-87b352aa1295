/*************************************************************
 *
 *  MathJax/localization/kn/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("kn","MathMenu",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Show: "\u0C97\u0CA3\u0CBF\u0CA4 \u0CB5\u0CA8\u0CCD\u0CA8\u0CC1 \u0CB9\u0CC7\u0C97\u0CC6 \u0CA4\u0CCB\u0CB0\u0CBF\u0CB8\u0CB2\u0CBF?",
          MathMLcode: "\u0CAE\u0CBE\u0CA4\u0CCD \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD  \u0CB8\u0CE6\u0C95\u0CC7\u0CA4",
          OriginalMathML: "\u0CAE\u0CC2\u0CB2 \u0CAE\u0CBE\u0CA4\u0CCD \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD",
          TeXCommands: "\u0C9F\u0CC6\u0C95\u0CCD\u0CB7\u0CCD 	\u0C85\u0CAA\u0CCD\u0CAA\u0CA3\u0CC6\u0C97\u0CB3\u0CC1",
          AsciiMathInput: "\u0C86\u0CB8\u0CCD\u0C9A\u0CC0 \u0CAE\u0CBE\u0CA4 \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD",
          Original: "\u0CAE\u0CC2\u0CB2 \u0CB0\u0CC2\u0CAA",
          ErrorMessage: "\u0CA4\u0CCD\u0CB0\u0CC1\u0C9F\u0CBF \u0CAA\u0CA4\u0CCD\u0CB0",
          Annotation: "\u0C9F\u0CBF\u0CAA\u0CCD\u0CAA\u0CA3\u0CBF",
          TeX: "\u0CA4\u0CC6\u0C95\u0CCD\u0CB7\u0CCD",
          StarMath: "\u0CB8\u0CCD\u0C9F\u0CBE\u0CB0\u0CCD \u0CAE\u0CBE\u0CA4\u0CCD",
          Maple: "\u0CAE\u0CC7\u0CAA\u0CB2\u0CCD",
          ContentMathML: "\u0CB5\u0CBF\u0CB7\u0CAF \u0CAE\u0CBE\u0CA4\u0CCD \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD",
          OpenMath: "\u0C93\u0CAA\u0CA8\u0CCD \u0CAE\u0CBE\u0CA4\u0CCD",
          texHints: "\u0CA4\u0CC6\u0C95\u0CCD\u0CB7\u0CCD \u0CB8\u0CC2\u0C9A\u0CCD\u0CAF\u0CB5\u0CBE\u0C97\u0CBF\u0C97\u0CB3\u0CA8\u0CCD\u0CA8\u0CC2 \u0CAE\u0CBE\u0CA4\u0CCD \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD \u0C85\u0CB2\u0CCD\u0CB2\u0CBF \u0CA4\u0CCB\u0CB0\u0CBF\u0CB8\u0CBF",
          Settings: "\u0C97\u0CA3\u0CBF\u0CA4 \u0CB8\u0CC6\u0C9F\u0CCD\u0C9F\u0CBF\u0C82\u0C97\u0CCD\u0C97\u0CB3\u0CC1",
          ZoomTrigger: "\u0C9C\u0CC2\u0CAE\u0CCD \u0C9F\u0CCD\u0CB0\u0CBF\u0C97\u0CB0\u0CCD",
          Hover: "\u0CB9\u0CCA\u0CB5\u0CC6\u0CB0\u0CCD",
          Click: "\u0C95\u0CCD\u0CB2\u0CBF\u0C95\u0CCD",
          DoubleClick: "\u0C8E\u0CB0\u0CA1\u0CC1 \u0C95\u0CCD\u0CB2\u0CBF\u0C95\u0CCD",
          NoZoom: "\u0C9C\u0CCB\u0CAE \u0C87\u0CB2\u0CCD\u0CB2",
          TriggerRequires: "\u0C9F\u0CCD\u0CB0\u0CBF\u0C97\u0CB0\u0CCD \u0C87\u0C97\u0CC6 \u0CAC\u0CC6\u0C95\u0C97\u0CC1\u0CA4\u0CA6\u0CC6:",
          Option: "\u0C86\u0CAF\u0CCD\u0C95\u0CC6",
          Alt: "Alt",
          Command: "Command",
          Control: "Control",
          Shift: "Shift",
          ZoomFactor: "\u0C9C\u0CCB\u0CAE \u0C85\u0CAA\u0CB5\u0CB0\u0CCD\u0CA4\u0CA8",
          Renderer: "\u0C97\u0CA3\u0CBF\u0CA4 \u0CAA\u0CCD\u0CB0\u0CA4\u0CBF\u0CAF\u0CBE\u0C97\u0CBF \u0C95\u0CCA\u0CA1\u0CC1\u0CB5\u0CB5",
          MPHandles: "\u0CAE\u0CA4\u0CCD \u0CAA\u0CCD\u0CB2\u0CC7\u0CAF\u0CB0\u0CCD \u0C8E\u0C82\u0CA4 \u0CA8\u0CBF\u0CB0\u0CCD\u0CB5\u0CB9\u0CBF\u0CB8\u0CB2\u0CC1 \u0CAE\u0CBE\u0CA1 \u0CAC\u0CC7\u0C95\u0CC1?",
          MenuEvents: "\u0CAE\u0CC6\u0CA8\u0CC1 \u0C98\u0C9F\u0CA8\u0CC6\u0C97\u0CB3\u0CC1",
          MouseEvents: "\u0CAE\u0CCC\u0CB8\u0CCD \u0C98\u0C9F\u0CA8\u0CC6\u0C97\u0CB3\u0CC1",
          MenuAndMouse: "\u0CAE\u0CCC\u0CB8\u0CCD \u0CAE\u0CA4\u0CCD\u0CA4\u0CC1 \u0CAE\u0CC6\u0CA8\u0CC1 \u0C98\u0C9F\u0CA8\u0CC6\u0C97\u0CB3\u0CC1",
          FontPrefs: "\u0CA4\u0CC8\u0CB2\u0CA6\u0CBE\u0CA8\u0CBF \u0C86\u0CAF\u0CBF\u0C95\u0CC6\u0C97\u0CB3\u0CC1",
          ForHTMLCSS: "\u0C8E\u0C9A\u0CCD \u0CA4\u0CC0 \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD / \u0CB8\u0CC0 \u0C8E\u0CB8\u0CCD\u0CB8\u0CCD \u0C8E\u0CB8\u0CCD\u0CB8\u0CCD \u0C87\u0C97\u0CC6",
          Auto: "\u0CB8\u0CCD\u0CB5\u0CAF\u0C82\u0C9A\u0CB2\u0CBF",
          TeXLocal: "\u0CA4\u0CC6\u0C95\u0CCD\u0CB7\u0CCD (\u0CB2\u0CCB\u0C95\u0CB2\u0CCD)",
          TeXWeb: "\u0CA4\u0CC6\u0C95\u0CCD\u0CB7\u0CCD (\u0CB5\u0CC6\u0CAC\u0CCD)",
          TeXImage: "\u0CA4\u0CC6\u0C95\u0CCD\u0CB7\u0CCD (\u0C87\u0CAE\u0CC7\u0C9C\u0CCD)",
          STIXLocal: "\u0CB7\u0CCD\u0CA4\u0CBF\u0C95\u0CCD\u0CB7\u0CCD (\u0CB2\u0CCB\u0C95\u0CB2\u0CCD)",
          STIXWeb: "\u0CB7\u0CCD\u0CA4\u0CBF\u0C95\u0CCD\u0CB7\u0CCD (\u0CB5\u0CC6\u0CAC\u0CCD)",
          AsanaMathWeb: "\u0C86\u0CB8\u0CA8 \u0CAE\u0CBE\u0CA4 (\u0CB5\u0CC6\u0CAC\u0CCD)",
          GyrePagellaWeb: "\u0C97\u0CCD\u0CAF\u0CCD\u0CB0\u0CC6 \u0CAA\u0C97\u0CC6\u0CB2\u0CCD\u0CB2 (\u0CB5\u0CC6\u0CAC\u0CCD)",
          GyreTermesWeb: "\u0C97\u0CCD\u0CAF\u0CCD\u0CB0\u0CC6 \u0C9F\u0CB0\u0CCD\u0CAE\u0CCD\u0CB8\u0CCD (\u0CB5\u0CC6\u0CAC\u0CCD)",
          LatinModernWeb: "\u0CB9\u0CCA\u0CB8 \u0CB2\u0CCD\u0CAF\u0CBE\u0C9F\u0CBF\u0CA8\u0CCD (\u0CB5\u0CC6\u0CAC\u0CCD)",
          NeoEulerWeb: "\u0CA8\u0CBF\u0CAF\u0CCB \u0C92\u0C87\u0CB2\u0CC6\u0CB0\u0CCD (\u0CB5\u0CC6\u0CAC\u0CCD)",
          ContextMenu: "\u0CB8\u0C82\u0CA6\u0CB0\u0CCD\u0CAD\u0CCB\u0C9A\u0CBF\u0CA4 \u0CAE\u0CC6\u0CA8\u0CC1",
          Browser: "\u0CAC\u0CCD\u0CB0\u0CCC\u0CB8\u0CB0\u0CCD",
          Scale: "\u0C8E\u0CB2\u0CCD\u0CB2 \u0C97\u0CA3\u0CBF\u0CA4 \u0CB5\u0CA8\u0CCD\u0CA8\u0CC1 \u0C88 \u0C85\u0CAA\u0CB5\u0CB0\u0CCD\u0CA4\u0CA8 \u0CA6\u0CBF\u0C82\u0CA6 \u0CAA\u0CCD\u0CB0\u0CAE\u0CBE\u0CA3\u0CA6 \u0CAE\u0CBE\u0CA1\u0CBF :",
          Discoverable: "\u0CB9\u0CCA\u0CB5\u0CC6\u0CB0\u0CCD \u0CAE\u0CBE\u0CA1\u0CBF\u0CA6\u0CBE\u0C97 \u0CB9\u0CC8\u0CB2\u0CC8\u0C9F\u0CCD \u0CAE\u0CBE\u0CA1\u0CBF",
          Locale: "\u0CAD\u0CBE\u0CB7\u0CC6",
          LoadLocale: "\u0CAF\u0CC2 \u0C86\u0CB0\u0CCD \u0C8E\u0CB2\u0CCD\u0CB2 \u0C87\u0C82\u0CA6 \u0CB2\u0CCB\u0CA1\u0CCD \u0CAE\u0CBE\u0CA1\u0CBF",
          About: "\u0CAE\u0CBE\u0CA4\u0CCD \u0C9C\u0C95\u0CCD\u0CB7\u0CCD \u0CAC\u0C97\u0CCD\u0C97\u0CC6",
          Help: "\u0CAE\u0CBE\u0CA4\u0CCD \u0C9C\u0C95\u0CCD\u0CB7\u0CCD \u0CB8\u0CB9\u0CAF\u0CA4\u0CC6",
          localTeXfonts: "\u0CB2\u0CCB\u0C95\u0CB2\u0CCD \u0CA4\u0CC6\u0C95\u0CCD\u0CB7\u0CCD \u0CAB\u0CBE\u0C82\u0C9F\u0CCD \u0C97\u0CB3\u0CA8\u0CCD\u0CA8 \u0C89\u0CAA\u0CAF\u0CCB\u0C97 \u0CAE\u0CBE\u0CA6\u0CCA\u0CA4 \u0C89\u0C82\u0C9F\u0CC1",
          webTeXfonts: "\u0CB5\u0CC6\u0CAC\u0CCD        \u0CA4\u0CC6\u0C95\u0CCD\u0CB7\u0CCD\u0C9F\u0CCD     \u0CAB\u0CBE\u0C82\u0C9F\u0CCD     \u0C97\u0CB3\u0CA8\u0CCD\u0CA8 \u0C89\u0CAA\u0CAF\u0CCB\u0C97 \u0CAE\u0CBE\u0CA6\u0CCA\u0CA4 \u0C89\u0C82\u0C9F\u0CC1",
          imagefonts: "\u0C87\u0CAE\u0CC7\u0C9C\u0CCD    \u0CAB\u0CBE\u0C82\u0C9F\u0CCD     \u0C97\u0CB3\u0CA8\u0CCD\u0CA8 \u0C89\u0CAA\u0CAF\u0CCB\u0C97 \u0CAE\u0CBE\u0CA6\u0CCA\u0CA4 \u0C89\u0C82\u0C9F\u0CC1",
          localSTIXfonts: "\u0CB2\u0CCB\u0C95\u0CB2\u0CCD \u0CB8\u0CCD\u0CA4\u0CBF\u0C95\u0CCD\u0CB7\u0CCD  \u0CAB\u0CBE\u0C82\u0C9F\u0CCD \u0C97\u0CB3\u0CA8\u0CCD\u0CA8 \u0C89\u0CAA\u0CAF\u0CCB\u0C97 \u0CAE\u0CBE\u0CA6\u0CCA\u0CA4 \u0C89\u0C82\u0C9F\u0CC1",
          webSVGfonts: "\u0CB5\u0CC6\u0CAC\u0CCD        \u0C8E\u0CB8 \u0CB5\u0CC7 \u0C9C\u0CC0     \u0CAB\u0CBE\u0C82\u0C9F\u0CCD     \u0C97\u0CB3\u0CA8\u0CCD\u0CA8 \u0C89\u0CAA\u0CAF\u0CCB\u0C97 \u0CAE\u0CBE\u0CA6\u0CCA\u0CA4 \u0C89\u0C82\u0C9F\u0CC1",
          genericfonts: "\u0C9C\u0CBE\u0CA4\u0CBF\u0CB5\u0CBF\u0CB6\u0CBF\u0CB8\u0CCD\u0CA4\u0CB5\u0CBE\u0CA6 \u0CAF\u0CC1\u0CA8\u0CBF\u0C95\u0CCB\u0CA1\u0CCD \u0CAB\u0CBE\u0C82\u0C9F\u0CCD \u0C97\u0CB3\u0CA8\u0CCD\u0CA8 \u0C89\u0CAA\u0CAF\u0CCB\u0C97 \u0CAE\u0CBE\u0CA1\u0CC1\u0CA4 \u0C89\u0C82\u0C9F\u0CC1",
          wofforotffonts: "\u0CB5\u0CCA\u0CAB\u0CCD\u0CAB\u0CCD \u0C85\u0CA5\u0CB5 \u0C92\u0CA4\u0CCD\u0CAB\u0CCD \u0CAB\u0CBE\u0C82\u0C9F\u0CCD \u0C97\u0CB3\u0CC1",
          eotffonts: "\u0C8E\u0C92\u0CA4\u0CCD \u0CAB\u0CBE\u0C82\u0C9F\u0CCD \u0C97\u0CB3\u0CC1",
          svgfonts: "\u0C8E\u0CB8 \u0CB5\u0CC7 \u0C9C\u0CC0 \u0CAB\u0CBE\u0C82\u0C9F\u0CCD \u0C97\u0CB3\u0CC1",
          WebkitNativeMMLWarning: "\u0CA8\u0CBF\u0CAE\u0CCD\u0CAE \u0CAC\u0CCD\u0CB0\u0CCC\u0CB8\u0CB0\u0CCD \u0C85\u0CA6\u0CB0 \u0C85\u0CB7\u0CCD\u0C9F\u0C95\u0CCD\u0C95\u0CC7 \u0CAE\u0CBE\u0CA4\u0CCD \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD \u0C85\u0CA8\u0CCD\u0CA8\u0CC1 \u0CA5\u0CCA\u0CB0\u0CBF\u0CB8\u0CC1\u0CA6\u0CBF\u0CB2\u0CCD\u0CB2. \u0C85\u0CA1\u0CCD\u0CA1\u0C95\u0CCD\u0C95\u0CC6 \u0CAE\u0CBE\u0CA4\u0CCD \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD \u0C94\u0C9F\u0CCD\u0CAA\u0CC1\u0C9F\u0CCD \u0C87\u0C97\u0CC6 \u0CB8\u0CCD\u0CB5\u0CBF\u0C9A\u0CCD \u0CAE\u0CBE\u0CA1\u0CBF\u0CA6\u0CB0\u0CC6, \u0C88 \u0CAA\u0CC1\u0C9F\u0CA6\u0CB2\u0CCD\u0CB2\u0CBF \u0C87\u0CA6\u0CCD\u0CA6 \u0C95\u0CC6\u0CB2\u0CB5 \u0C97\u0CA3\u0CBF\u0CA4 \u0C93\u0CA6\u0CB2\u0CBF\u0C95\u0CCD\u0C95\u0CC6 \u0C86\u0C97\u0CA6\u0CC6 \u0C87\u0CB0\u0CAC\u0CB9\u0CC1\u0CA6\u0CC1.",
          MSIENativeMMLWarning: "\u0C87\u0C82\u0C9F\u0CB0\u0CCD\u0CA8\u0CC6\u0C9F\u0CCD \u0C8E\u0C95\u0CCD\u0CB8\u0CCD\u0CAA\u0CCD\u0CB2\u0CCB\u0CB0\u0CB0\u0CCD \u0C87\u0C97\u0CC6 \u0CAE\u0CBE\u0CA4\u0CCD \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD \u0C94\u0C9F\u0CCD\u0CAA\u0CC1\u0C9F\u0CCD \u0C85\u0CA8\u0CCD\u0CA8\u0CC1 \u0CAA\u0CCD\u0CB0\u0C95\u0CCD\u0CB0\u0CBF\u0CAF \u0CAE\u0CBE\u0CA1\u0CB2\u0CBF\u0C95\u0CCD\u0C95\u0CC6 \u0CAE\u0CBE\u0CA4\u0CCD \u0CAA\u0CCD\u0CB2\u0CC7\u0CAF\u0CB0\u0CCD \u0CAA\u0CCD\u0CB2\u0C97\u0CCD-\u0C87\u0CA8\u0CCD \u0CAC\u0CC6\u0C95\u0CC1.",
          OperaNativeMMLWarning: "\u0CAE\u0CBE\u0CA4\u0CCD \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD \u0C87\u0C97\u0CC6 \u0C92\u0CAA\u0CC6\u0CB0\u0CA6\u0CCD\u0CA6\u0CC1 \u0CAC\u0CC6\u0C82\u0CAC\u0CB2 \u0C89\u0C82\u0C9F\u0CC1, \u0C85\u0CA6\u0C95\u0CCD\u0C95\u0CC6 \u0CAE\u0CBE\u0CA4\u0CCD \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD \u0C94\u0C9F\u0CCD\u0CAA\u0CC1\u0C9F\u0CCD \u0C87\u0C97\u0CC6 \u0CB8\u0CCD\u0CB5\u0CBF\u0C9A\u0CCD \u0CAE\u0CBE\u0CA6\u0CC1\u0CA6\u0CB0\u0CBF\u0C82\u0CA6 \u0C95\u0CC6\u0CB2\u0CCD\u0CB2\u0CB5 \u0CB5\u0CBF\u0CB7\u0CAF \u0C97\u0CB3\u0CC1 \u0C95\u0CC6\u0C9F\u0CCD\u0C9F \u0CB0\u0CC0\u0CA4\u0CBF\u0C85\u0CB2\u0CCD\u0CB2\u0CBF \u0CB0\u0CC6\u0CA8\u0CCD\u0CA6\u0CC6\u0CB0\u0CCD \u0C86\u0C97 \u0CAC\u0CB9\u0CC1\u0CA6\u0CC1.",
          SafariNativeMMLWarning: "\u0CA8\u0CBF\u0CAE\u0CCD\u0CAE \u0CAC\u0CCD\u0CB0\u0CCC\u0CB8\u0CB0\u0CCD \u0C87\u0CA8 \u0C85\u0CA6\u0CB0\u0CA6\u0CCD\u0CA6\u0CC7 \u0CAE\u0CBE\u0CA4\u0CCD \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD \u0CAE\u0CBE\u0CA4\u0CCD \u0C9C\u0C95\u0CCD\u0CB7\u0CCD \u0C89\u0CAA\u0CAF\u0CCB\u0C97 \u0CAE\u0CBE\u0CA1\u0CC1\u0CB5\u0C82\u0CA4 \u0C8E\u0CB2\u0CCD\u0CB2 \u0CAB\u0CC0\u0C9A\u0CB0\u0CCD \u0C97\u0CB3\u0CA8\u0CCD\u0CA8 \u0CAC\u0CBF\u0CA6\u0CC1\u0CA6\u0CBF\u0CB2\u0CCD\u0CB2. \u0C85\u0CA1\u0CCD\u0CA1\u0C95\u0CCD\u0C95\u0CC6, \u0C95\u0CC6\u0CB2\u0CB5 \u0C97\u0CA3\u0CBF\u0CA4 \u0CB8\u0CB0\u0CBF \u0C86\u0C97\u0CBF \u0C95\u0CBE\u0CA3\u0CA6\u0CC6 \u0C87\u0CB0 \u0CAC\u0CB9\u0CC1\u0CA6\u0CC1.",
          FirefoxNativeMMLWarning: "\u0CA8\u0CBF\u0CAE\u0CCD\u0CAE \u0CAC\u0CCD\u0CB0\u0CCC\u0CB8\u0CB0\u0CCD \u0C87\u0CA8 \u0C85\u0CA6\u0CB0\u0CA6\u0CCD\u0CA6\u0CC7 \u0CAE\u0CBE\u0CA4\u0CCD \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD \u0CAE\u0CBE\u0CA4\u0CCD \u0C9C\u0C95\u0CCD\u0CB7\u0CCD \u0C89\u0CAA\u0CAF\u0CCB\u0C97 \u0CAE\u0CBE\u0CA1\u0CC1\u0CB5\u0C82\u0CA4 \u0C8E\u0CB2\u0CCD\u0CB2 \u0CAB\u0CC0\u0C9A\u0CB0\u0CCD \u0C97\u0CB3\u0CA8\u0CCD\u0CA8 \u0CAC\u0CBF\u0CA6\u0CC1\u0CA6\u0CBF\u0CB2\u0CCD\u0CB2. \u0C85\u0CA1\u0CCD\u0CA1\u0C95\u0CCD\u0C95\u0CC6, \u0C95\u0CC6\u0CB2\u0CB5 \u0C97\u0CA3\u0CBF\u0CA4 \u0CB8\u0CB0\u0CBF \u0C86\u0C97\u0CBF \u0C95\u0CBE\u0CA3\u0CA6\u0CC6 \u0C87\u0CB0 \u0CAC\u0CB9\u0CC1\u0CA6\u0CC1.",
          MSIESVGWarning: "\u0C8E\u0CB8\u0CCD  \u0CB5\u0CBF \u0C9C\u0CBF \u0C9A\u0CBF\u0CA4\u0CCD\u0CB0 \u0C97\u0CB3\u0CA8\u0CCD\u0CA8\u0CC2 \u0CA8\u0CBF\u0CAE\u0CCD\u0CAE \u0CAC\u0CCD\u0CB0\u0CCC\u0CB8\u0CB0\u0CCD (\u0C87\u0C82\u0C9F\u0CB0\u0CCD\u0CA8\u0CC6\u0C9F\u0CCD \u0C8E\u0C95\u0CCD\u0CB8\u0CCD\u0CAA\u0CCD\u0CB2\u0CCB\u0CB0\u0CB0\u0CCD) \u0CAC\u0CB0\u0CBF \u0C90\u0C88\u0CEF \u0C85\u0CB2\u0CCD\u0CB2\u0CBF \u0CAE\u0CBE\u0CA4\u0CCD\u0CB0 \u0CB8\u0CAA\u0CCB\u0CB0\u0CCD\u0C9F\u0CCD \u0C86\u0C97\u0CC1\u0CA4\u0CA6\u0CC6. \u0C90\u0C88\u0CEF \u0C95\u0CBF\u0C82\u0CA4 \u0C95\u0CAE\u0CCD\u0CAE\u0CBF \u0CB5\u0CB0\u0CCD\u0CB7\u0CA8\u0CCD \u0C97\u0CB3\u0CA8\u0CCD\u0CA8 \u0CB8\u0CBF\u0CAE\u0CC1\u0CB3\u0CC6\u0C9F\u0CCD \u0CAE\u0CBE\u0CA1\u0CC1\u0CB5\u0CBE\u0C97 \u0CB8\u0CB9 \u0CB9\u0CBE\u0C97\u0CC6 \u0C86\u0C97\u0CC1\u0CA4\u0CA6\u0CC6. \u0C85\u0CA6\u0C95\u0CCD\u0C95\u0CC6 \u0C8E\u0CB8 \u0CB5\u0CBF \u0C9C\u0CC0 \u0C87\u0C97\u0CC6 \u0CAC\u0CA6\u0CB2\u0CBF\u0CB8\u0CBF\u0CA6\u0CB0\u0CC6 \u0C95\u0CC6\u0CB2\u0CB5 \u0C97\u0CA3\u0CBF\u0CA4 \u0CB8\u0CB0\u0CBF \u0C86\u0C97\u0CBF \u0C95\u0CBE\u0CA3\u0CA6\u0CC6 \u0C87\u0CB0 \u0CAC\u0CB9\u0CC1\u0CA6\u0CC1.",
          LoadURL: "\u0C88 \u0CAF\u0CC1 \u0C86\u0CB0\u0CCD \u0C8E\u0CB2\u0CCD \u0C87\u0C82\u0CA6 \u0CA1\u0CBE\u0C9F\u0CBE \u0CB2\u0CCB\u0CA1\u0CCD \u0CAE\u0CBE\u0CA1\u0CC1\u0CA4 \u0C89\u0C82\u0C9F\u0CC1",
          BadURL: "\u0C88 \u0CAF\u0CC1\u0C86\u0CB0\u0CCD\u0C8E\u0CB2\u0CCD \u0C92\u0C82\u0CA6\u0CC1 \u0C9C\u0CBE\u0CB5\u0CBE\u0CB8\u0CCD\u0C95\u0CCD\u0CB0\u0CBF\u0CAA\u0CCD\u0C9F\u0CCD \u0CAB\u0CC8\u0CB2\u0CCD \u0C87\u0C97\u0CC6 \u0CB9\u0CCB\u0C97\u0CAC\u0CC7\u0C95\u0CC1 \u0CAF\u0CBE\u0CB5\u0CA6\u0CC1 \u0CAE\u0CBE\u0CA4\u0CCD \u0C9C\u0C95\u0CCD\u0CB7\u0CCD \u0C87\u0CA8 \u0C9F\u0CCD\u0CB0\u0CBE\u0CA8\u0CCD\u0CB8\u0CCD\u0CB2\u0CC7\u0CB7\u0CA8\u0CCD \u0CA1\u0CC7\u0C9F\u0CBE\u0CB5\u0CA8\u0CCD\u0CA8\u0CC1 \u0CB9\u0CBF\u0CA1\u0CC1\u0C95\u0CCA\u0CB3\u0CCD\u0CB3\u0CC1\u0CA4\u0CA6\u0CC6. \u0C85\u0C82\u0CA4 \u0CAB\u0CC8\u0CB2\u0CCD \u0C87\u0CA8 \u0CB9\u0CC6\u0CB8\u0CB0\u0CC1 \u0C92\u0C82\u0CA6\u0CC1 \".js\" \u0C8E\u0C95\u0CCD\u0CB8\u0C9F\u0CC6\u0CA8\u0CCD\u0CB6\u0CA8\u0CCD \u0C92\u0C9F\u0CCD\u0C9F\u0CBF\u0C97\u0CC6 \u0CAE\u0CC1\u0C97\u0CBF\u0CA4\u0CA6\u0CC6,",
          BadData: "%1 \u0C87\u0C82\u0CA6 \u0C85\u0CA8\u0CC1\u0CB5\u0CBE\u0CA7 \u0CA1\u0CBE\u0C9F\u0CBE \u0CB2\u0CCB\u0CA1\u0CCD \u0CAE\u0CBE\u0CA1\u0CB2\u0CBF\u0C95\u0CCD\u0C95\u0CC6 \u0C86\u0C97\u0CB2\u0CBF\u0CB2\u0CCD\u0CB2",
          SwitchAnyway: "\u0C86\u0CA6\u0CB0\u0CC1\u0CB8\u0CB9 \u0CB0\u0CC6\u0CA8\u0CCD\u0CA6\u0CC6\u0CB0\u0CC6\u0CB0\u0CCD \u0CAC\u0CA6\u0CB2\u0CBE\u0CAF\u0CBF\u0CB8 \u0CAC\u0CC6\u0C95?",
          ScaleMath: "\u0CAF\u0CB2\u0CCD\u0CB2 \u0C97\u0CA3\u0CBF\u0CA4 \u0CB5\u0CA8\u0CCD\u0CA8\u0CC1 \u0CAC\u0CBE\u0C95\u0CBF \u0CAA\u0CA5\u0CCD\u0CAF\u0C95\u0CCD\u0C95\u0CC6  \u0CB9\u0CCB\u0CB2\u0CBF\u0C95\u0CC6\u0CAF\u0CB2\u0CCD\u0CB2\u0CBF \u0C87\u0CB7\u0CCD\u0C9F\u0CC1 \u0C85\u0CAA\u0CB5\u0CB0\u0CCD\u0CA4\u0CA8\u0CA6\u0CBF\u0C82\u0CA6 \u0CA6\u0CCA\u0CA1\u0CCD\u0CA1\u0CA6\u0CC1 \u0C85\u0CA5\u0CB5\u0CBE \u0CB8\u0CC7\u0CA8\u0CCD\u0CA8\u0CA6\u0CC1 \u0CAE\u0CBE\u0CA6\u0CBF.",
          NonZeroScale: "\u0CB8\u0CCD\u0C95\u0CC7\u0CB2\u0CCD \u0CB8\u0CCA\u0CA8\u0CCD\u0CA8\u0CC6 \u0C87\u0CB0 \u0CAC\u0CBE\u0CB0\u0CA6\u0CC1",
          PercentScale: "\u0CB8\u0CCD\u0C95\u0CC7\u0CB2\u0CCD \u0C92\u0C82\u0CA6\u0CC1 \u0CAA\u0CB0\u0CCD\u0CB8\u0C82\u0C9F\u0CC7\u0C9C\u0CCD \u0C87\u0CB0\u0CAC\u0CC7\u0C95\u0CC1 (\u0C89\u0CA6\u0CBE\u0CB9\u0CB0\u0CA3: \u0CE7\u0CE8\u0CE6%%)",
          NoOriginalForm: "\u0CAE\u0CC2\u0CB2 \u0CB0\u0CC2\u0CAA \u0CB2\u0CAD\u0CCD\u0CAF\u0CB5\u0CBF\u0CB0\u0CB5 \u0C87\u0CB2\u0CCD\u0CB2",
          Close: "\u0CAE\u0CC1\u0C9A\u0CCD\u0C9A\u0CBF",
          EqSource: "\u0CAE\u0CBE\u0CA4\u0CCD \u0C9C\u0C95\u0CCD\u0CB7\u0CCD \u0C8E\u0C95\u0CBC\u0CC1\u0C85\u0CA4\u0CBF\u0C92\u0CA8\u0CCD \u0CB8\u0CCA\u0CB0\u0CCD\u0CB8\u0CCD"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/kn/MathMenu.js");
