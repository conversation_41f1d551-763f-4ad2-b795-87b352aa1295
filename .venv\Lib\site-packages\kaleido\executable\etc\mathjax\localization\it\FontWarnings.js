/*************************************************************
 *
 *  MathJax/localization/it/FontWarnings.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("it","FontWarnings",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          webFont: "MathJax sta usando dei web font per visualizzare le formule di questa pagina. Tali font richiedono tempo per essere scaricati, perci\u00F2 la pagina sarebbe resa pi\u00F9 velocemente se tu installassi dei font matematici direttamente nella cartella dei font di sistema.",
          imageFonts: "MathJax sta usando dei font immagine invece di quelli locali o dei web font. Questo rallenta la resa oltremodo e le formule potrebbero non essere stampate alla massima risoluzione dalla tua stampante.",
          noFonts: "MathJax non \u00E8 in grado di trovare un font adatto a visualizzare le formule e i font immagini non sono disponibili; perci\u00F2 utilizzer\u00E1 dei generici caratteri unicode sperando che il tuo browser sia in grado di visualizzarli. Alcuni caratteri potrebbero non essere mostrati correttamente o mancare del tutto.",
          webFonts: "I browser attuali permettono di scaricare i font dal web. Aggiornando il tuo browser a una versione pi\u00F9 recente (o cambiando del tutto browser) la qualit\u00E1 delle formule di questa pagina potrebbe migliorare.",
          fonts: "MathJax pu\u00F2 usare sia gli [STIX font](%1) che i [MathJax TeX font](%2). Scarica e installa uno di questi font per avere una resa migliore da MathJax.",
          STIXPage: "Questa pagina richiede l'uso degli [STIX font](%1). Scarica e installa i suddetti font per avere una resa migliore da MathJax.",
          TeXPage: "Questa pagina richiede l'uso dei [MathJax TeX font](%1). Scarica e installa i suddetti font per avere una resa migliore da MathJax."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/it/FontWarnings.js");
