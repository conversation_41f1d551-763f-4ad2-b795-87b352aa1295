/*************************************************************
 *
 *  MathJax/localization/pl/TeX.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("pl","TeX",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          ExtraOpenMissingClose: "Nadmiarowy nawias otwieraj\u0105cy lub brakuj\u0105cy nawias zam<PERSON>\u0105cy",
          ExtraCloseMissingOpen: "Nadmiarowy nawias zamykaj\u0105cy lub brakuj\u0105cy nawias otwieraj\u0105cy",
          MissingLeftExtraRight: "Brakuj\u0105cy \\left lub nadmiarowy \\right",
          MissingScript: "Brakuj\u0105cy argument dla indeksu dolnego lub g\u00F3rnego",
          ExtraLeftMissingRight: "Nadmiarowy \\left lub brakuj\u0105cy \\right",
          Misplaced: "\u0179le umiejscowiony %1",
          MissingOpenForSub: "Brakuj\u0105cy nawias otwieraj\u0105cy dla indeksu dolnego",
          MissingOpenForSup: "Brakuj\u0105cy nawias otwieraj\u0105cy dla indeksu g\u00F3rnego",
          AmbiguousUseOf: "Niejednoznaczne u\u017Cycie %1",
          EnvBadEnd: "\\begin{%1} zosta\u0142 zako\u0144czony \\end{%2}",
          EnvMissingEnd: "Brakuj\u0105ce \\end{%1}",
          MissingBoxFor: "Brakuj\u0105ce pude\u0142ko (box) dla %1",
          MissingCloseBrace: "Brakuj\u0105cy nawias zamykaj\u0105cy",
          UndefinedControlSequence: "Nieznana komenda steruj\u0105ca %1",
          DoubleExponent: "Podw\u00F3jna pot\u0119ga: u\u017Cyj nawias\u00F3w, aby unikn\u0105\u0107 niejednoznaczno\u015Bci",
          DoubleSubscripts: "Podw\u00F3jny indeks: u\u017Cyj nawias\u00F3w, aby unikn\u0105\u0107 niejednoznaczno\u015Bci",
          DoubleExponentPrime: "Znak ' oznacza podw\u00F3jny wyk\u0142adnik: u\u017Cyj nawias\u00F3w, aby unikn\u0105\u0107 niejednoznaczno\u015Bci",
          CantUseHash1: "Nie mo\u017Cesz u\u017Cywa\u0107 znaku parametru makra # w trybie matematycznym",
          MisplacedMiddle: "%1 musi by\u0107 pomi\u0119dzy \\left i \\right",
          MisplacedLimits: "%1 jest dozwolony tylko dla operator\u00F3w",
          MisplacedMoveRoot: "%1 mo\u017Ce pojawi\u0107 si\u0119 tylko w ramach korzenia (root)",
          MultipleCommand: "Wielokrotny %1",
          IntegerArg: "Parametr dla %1 musi by\u0107 liczb\u0105 ca\u0142kowit\u0105",
          NotMathMLToken: "%1 nie jest elementem typu token",
          InvalidMathMLAttr: "Nieprawid\u0142owy atrybut MathML: %1",
          UnknownAttrForElement: "%1 nie jest znanym atrybutem dla %2",
          MaxMacroSub1: "Przekroczono maksymaln\u0105 liczb\u0119 wywo\u0142a\u0144 makra; czy wyst\u0119puje rekursywne makro?",
          MaxMacroSub2: "Przekroczono maksymaln\u0105 liczb\u0119 zast\u0105pie\u0144 MathJax; czy wyst\u0119puje rekursywne \u015Brodowisko LaTeX?",
          MissingArgFor: "Brakuje argumentu dla %1",
          ExtraAlignTab: "Nadmiarowy tabulator w \\cases",
          BracketMustBeDimension: "Argument w nawiasie dla %1 musi by\u0107 wymiarem",
          InvalidEnv: "Nieznana nazwa \u015Brodowiska '%1'",
          UnknownEnv: "Nieznane \u015Brodowisko '%1'",
          ExtraCloseLooking: "Nadmiarowy nawias zamykaj\u0105cy napotkany w czasie poszukiwania %1",
          MissingCloseBracket: "Nie odnaleziono zamykaj\u0105cego nawiasu ']' dla argumentu dla %1",
          MissingOrUnrecognizedDelim: "Nieodnaleziony lub nierozpoznany separator dla %1",
          MissingDimOrUnits: "Brakuje wymiaru lub jego jednostki dla %1",
          TokenNotFoundForCommand: "Nie odnaleziono %1 dla %2",
          MathNotTerminated: "Nie odnaleziono zako\u0144czenia w polu tekstowym",
          IllegalMacroParam: "Nieprawid\u0142owa referencja do parametru makra",
          MaxBufferSize: "Przekroczono rozmiar bufora MathJax, czy istnieje rekursywne wywo\u0142anie makra?",
          CommandNotAllowedInEnv: "%1 nie jest dozwolony w \u015Brodowisku %2",
          MultipleLabel: "Wielokrotna definicja etykiety '%1'",
          CommandAtTheBeginingOfLine: "%1 musi znajdowa\u0107 si\u0119 na pocz\u0105tku linii",
          IllegalAlign: "Nieprawid\u0142owy argument dla %1",
          BadMathStyleFor: "B\u0142\u0119dny styl dla %1",
          PositiveIntegerArg: "Argument dla %1 musi by\u0107 dodatni\u0105 liczb\u0105 ca\u0142kowit\u0105",
          ErroneousNestingEq: "B\u0142\u0119dne zagnie\u017Cd\u017Cenie wyra\u017Ce\u0144",
          MultlineRowsOneCol: "Wiersze w \u015Brodowisku %1 musz\u0105 mie\u0107 dok\u0142adnie jedn\u0105 kolumn\u0119",
          MultipleBBoxProperty: "%1 okre\u015Blony dwa razy w %2",
          InvalidBBoxProperty: "'%1' nie jest kolorem, wielko\u015Bci\u0105 odst\u0119pu, ani stylem",
          ExtraEndMissingBegin: "Nadmiarowy %1 lub brakuj\u0105cy \\begingroup",
          GlobalNotFollowedBy: "Po %1 nie wyst\u0119puje \\let, \\def, ani \\newcommand",
          UndefinedColorModel: "Przestrze\u0144 barw '%1' nie jest zdefiniowana",
          ModelArg1: "Warto\u015Bci kolor\u00F3w dla przestrzeni %1 wymagaj\u0105 3 liczb",
          InvalidDecimalNumber: "Nieprawid\u0142owe liczba dziesi\u0119tna",
          ModelArg2: "Warto\u015Bci kolor\u00F3w dla przestrzeni %1 musz\u0105 by\u0107 pomi\u0119dzy %2 i %3",
          InvalidNumber: "B\u0142\u0119dna liczba",
          NewextarrowArg1: "Pierwszy argument dla %1 musi by\u0107 nazw\u0105 sekwencji kontrolnej",
          NewextarrowArg2: "Drugi argumentem dla %1 musz\u0105 by\u0107 dwie liczby ca\u0142kowite oddzielone przecinkiem",
          NewextarrowArg3: "Trzeci argument dla %1 musi by\u0107 numerem znaku unicode",
          NoClosingChar: "Nie mo\u017Cna odnale\u017A\u0107 zamykaj\u0105cego %1",
          IllegalControlSequenceName: "Nieprawid\u0142owa nazwa sekwencji kontrolnej dla %1",
          IllegalParamNumber: "Nieprawid\u0142owa liczba parametr\u00F3w dla %1",
          MissingCS: "Po %1 musi wyst\u0105pi\u0107 sekwencja kontrolna",
          CantUseHash2: "Nieprawid\u0142owe u\u017Cycie # w szablonie dla %1",
          SequentialParam: "Parametry dla %1 musz\u0105 by\u0107 ponumerowane rosn\u0105co",
          MissingReplacementString: "Brak \u0142a\u0144cucha zamiennego dla definicji %1",
          MismatchUseDef: "U\u017Cycie %1 niezgodne z definicj\u0105",
          RunawayArgument: "Zgin\u0105\u0142 argument dla %1?",
          NoClosingDelim: "Nie mo\u017Cna znale\u017A\u0107\u00A0ko\u0144cz\u0105cego separatora dla %1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/pl/TeX.js");
