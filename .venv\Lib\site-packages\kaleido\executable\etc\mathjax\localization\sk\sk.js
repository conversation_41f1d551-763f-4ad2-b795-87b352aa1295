/*************************************************************
 *
 *  MathJax/localization/sk/sk.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("sk",null,{
  menuTitle: "sloven\u010Dina",
  version: "2.7.5",
  isLoaded: true,
  domains: {
    "_": {
        version: "2.7.5",
        isLoaded: true,
        strings: {
          MathProcessingError: "Chyba spracovania matematiky",
          MathError: "Chyba matematiky",
          LoadFile: "Na\u010D\u00EDtava sa %1",
          Loading: "Na\u010D\u00EDtavam",
          LoadFailed: "Nepodarilo sa na\u010D\u00EDta\u0165 s\u00FAbor: %1",
          ProcessMath: "Spracuje sa matematika: %1 %%",
          Processing: "Sprac\u00FAvam",
          TypesetMath: "S\u00E1dzanie matematiky: %1 %%",
          Typesetting: "S\u00E1dzanie",
          MathJaxNotSupported: "V\u00E1\u0161 prehliada\u010D nepodporuje MathJax",
          ErrorTips: "Tipy k ladeniu: pou\u017Eite %%1, prehliadajte %%2 v konzole prehliada\u010Da"
        }
    },
    "FontWarnings": {},
    "HTML-CSS": {},
    "HelpDialog": {},
    "MathML": {},
    "MathMenu": {},
    "TeX": {}
  },
  plural: function (n) {
      if (n === 1) {return 1} // one
      if (n === 2 || n === 3 || n === 4) {return 2} // two--four
      return 3; // other
    },
  number: function (n) {
      return String(n).replace(".", ","); // replace dot by comma
    }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/sk/sk.js");
