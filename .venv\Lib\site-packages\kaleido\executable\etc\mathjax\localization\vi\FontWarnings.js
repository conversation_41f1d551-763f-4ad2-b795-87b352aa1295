/*************************************************************
 *
 *  MathJax/localization/vi/FontWarnings.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("vi","FontWarnings",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          webFont: "MathJax s\u1EED d\u1EE5ng c\u00E1c ph\u00F4ng ch\u1EEF tr\u00EAn Web \u0111\u1EC3 v\u1EBD k\u00FD hi\u1EC7u to\u00E1n h\u1ECDc tr\u00EAn trang n\u00E0y. Tr\u00ECnh duy\u1EC7t ph\u1EA3i t\u1EF1 \u0111\u1ED9ng t\u1EA3i v\u1EC1 c\u00E1c ph\u00F4ng ch\u1EEF n\u00E0y; \u0111\u1EC3 l\u00E0m cho trang hi\u1EC3n th\u1ECB nhanh h\u01A1n, b\u1EA1n c\u00F3 th\u1EC3 c\u00E0i \u0111\u1EB7t c\u00E1c ph\u00F4ng ch\u1EEF to\u00E1n h\u1ECDc tr\u1EF1c ti\u1EBFp v\u00E0o th\u01B0 m\u1EE5c ph\u00F4ng ch\u1EEF c\u1EE7a m\u00E1y.",
          imageFonts: "MathJax s\u1EED d\u1EE5ng c\u00E1c ph\u00F4ng ch\u1EEF h\u00ECnh \u1EA3nh c\u1EE7a n\u00F3 thay v\u00EC c\u00E1c ph\u00F4ng ch\u1EEF tr\u00EAn m\u00E1y ho\u1EB7c tr\u00EAn Web. C\u00E1c bi\u1EC3u th\u1EE9c to\u00E1n h\u1ECDc n\u00E0y s\u1EBD hi\u1EC3n th\u1ECB ch\u1EADm h\u01A1n b\u00ECnh th\u01B0\u1EDDng v\u00E0 c\u00F3 th\u1EC3 in ra \u1EDF \u0111\u1ED9 ph\u00E2n gi\u1EA3i th\u1EA5p.",
          noFonts: "MathJax kh\u00F4ng t\u00ECm th\u1EA5y ph\u00F4ng ch\u1EEF \u0111\u1EC3 hi\u1EC3n th\u1ECB to\u00E1n h\u1ECDc, v\u00E0 c\u00E1c ph\u00F4ng ch\u1EEF h\u00ECnh \u1EA3nh kh\u00F4ng c\u00F3 s\u1EB5n, n\u00EAn n\u00F3 s\u1EED d\u1EE5ng c\u00E1c k\u00FD t\u1EF1 Unicode b\u00ECnh th\u01B0\u1EDDng n\u1EBFu tr\u01B0\u1EDDng h\u1EE3p tr\u00ECnh duy\u1EC7t c\u00F3 kh\u1EA3 n\u0103ng hi\u1EC3n th\u1ECB ch\u00FAng. M\u1ED9t s\u1ED1 k\u00FD t\u1EF1 s\u1EBD kh\u00F4ng hi\u1EC3n th\u1ECB ho\u1EB7c kh\u00F4ng hi\u1EC3n th\u1ECB ch\u00EDnh x\u00E1c.",
          webFonts: "H\u1EA7u h\u1EBFt c\u00E1c tr\u00ECnh duy\u1EC7t hi\u1EC7n \u0111\u1EA1i cho ph\u00E9p t\u1EA3i v\u1EC1 ph\u00F4ng ch\u1EEF t\u1EEB trang m\u1EA1ng. Vi\u1EC7c c\u1EADp nh\u1EADt \u0111\u1EBFn m\u1ED9t phi\u00EAn b\u1EA3n tr\u00ECnh duy\u1EC7t m\u1EDBi h\u01A1n (ho\u1EB7c \u0111\u1ED5i qua tr\u00ECnh duy\u1EC7t kh\u00E1c) c\u00F3 th\u1EC3 c\u1EA3i thi\u1EC7n ch\u1EA5t l\u01B0\u1EE3ng c\u1EE7a to\u00E1n tr\u00EAn trang n\u00E0y.",
          fonts: "MathJax c\u00F3 th\u1EC3 s\u1EED d\u1EE5ng [c\u00E1c ph\u00F4ng ch\u1EEF STIX](%1) ho\u1EB7c [c\u00E1c ph\u00F4ng ch\u1EEF TeX MathJax](%2). H\u00E3y t\u1EA3i v\u1EC1 v\u00E0 c\u00E0i \u0111\u1EB7t m\u1ED9t trong nh\u1EEFng ph\u00F4ng ch\u1EEF n\u00E0y \u0111\u1EC3 c\u1EA3i thi\u1EC7n kinh nghi\u1EC7m MathJax c\u1EE7a b\u1EA1n.",
          STIXPage: "Trang n\u00E0y \u0111\u01B0\u1EE3c thi\u1EBFt k\u1EBF \u0111\u1EC3 s\u1EED d\u1EE5ng [c\u00E1c ph\u00F4ng ch\u1EEF STIX](%1). H\u00E3y t\u1EA3i v\u1EC1 v\u00E0 c\u00E0i \u0111\u1EB7t nh\u1EEFng ph\u00F4ng ch\u1EEF n\u00E0y \u0111\u1EC3 c\u1EA3i thi\u1EC7n kinh nghi\u1EC7m MathJax c\u1EE7a b\u1EA1n.",
          TeXPage: "Trang n\u00E0y \u0111\u01B0\u1EE3c thi\u1EBFt k\u1EBF \u0111\u1EC3 s\u1EED d\u1EE5ng [c\u00E1c ph\u00F4ng ch\u1EEF TeX MathJax](%1). H\u00E3y t\u1EA3i v\u1EC1 v\u00E0 c\u00E0i \u0111\u1EB7t nh\u1EEFng ph\u00F4ng ch\u1EEF n\u00E0y \u0111\u1EC3 c\u1EA3i thi\u1EC7n kinh nghi\u1EC7m MathJax c\u1EE7a b\u1EA1n."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/vi/FontWarnings.js");
