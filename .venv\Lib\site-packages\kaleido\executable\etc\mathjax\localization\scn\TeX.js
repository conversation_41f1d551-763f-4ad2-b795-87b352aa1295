/*************************************************************
 *
 *  MathJax/localization/scn/TeX.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("scn","TeX",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          ExtraOpenMissingClose: "Par\u00E8ntisi graffa graputa suverchia o par\u00E8ntisi graffa chiusa ammancanti",
          ExtraCloseMissingOpen: "Par\u00E8ntisi graffa chiusa suverchia o par\u00E8ntisi graffa graputa ammancanti",
          MissingLeftExtraRight: "Cumannu \\left ammancanti o cumannu \\right suverchiu",
          MissingScript: "Argumentu ammancanti p\u00FB spunenti o p\u00FB dipunenti",
          ExtraLeftMissingRight: "Cumannu \\left suverchiu o cumannu \\right ammancanti",
          Misplaced: "%1 fora postu",
          MissingOpenForSub: "Par\u00E8ntisi graffa graputa ammancanti p\u00FB dipunenti",
          MissingOpenForSup: "Par\u00E8ntisi graffa graputa ammancanti p\u00F4 spunenti",
          AmbiguousUseOf: "Cumannu %1 adupiratu di manera amb\u00ECgua",
          EnvBadEnd: "Cumannu \\begin{%1} cunchiusu d\u00FB cumannu \\end{%2}",
          EnvMissingEnd: "Cumannu \\end{%1} ammancanti",
          MissingBoxFor: "Buatta ammancanti p\u00FB cumannu %1",
          MissingCloseBrace: "Par\u00E8ntisi graffa chiusa ammancanti",
          UndefinedControlSequence: "Siquenza di cuntrollu nun difinuta %1",
          DoubleExponent: "Spunenti duppiu: adupirari li par\u00E8ntisi graffi pi chiariri",
          DoubleSubscripts: "Dipunenti duppiu: adupirari li par\u00E8ntisi graffi pi chiariri",
          DoubleExponentPrime: "Spunenti duppiu p\u00FB s\u00ECmmulu di primu: adupirari li par\u00E8ntisi graffi pi chiariri",
          CantUseHash1: "Nun si p\u00F2 adupirari lu car\u00E0ttiri # comu par\u00E0mitru di macro nt\u00E2 mudalit\u00E0 matim\u00E0tica",
          MisplacedMiddle: "Lu cumannu %1 s'havi a attruvari dintra d\u00EE cumanni \\left e \\right",
          MisplacedLimits: "Lu cumannu %1 \u00E8 cunzintutu sulu nta l'opiratura",
          MisplacedMoveRoot: "Lu cumannu %1 p\u00F2 cump\u00E0riri sulu sutta r\u00E0dici",
          MultipleCommand: "%1 ripitutu",
          IntegerArg: "L'argumentu d\u00FB cumannu %1 havi a \u00E8ssiri nu nteru",
          NotMathMLToken: "%1 nun \u00E8 n'elimentu di token",
          InvalidMathMLAttr: "Attribbutu MathML nun v\u00E0lidu: %1",
          UnknownAttrForElement: "%1 nun \u00E8 n\u2019attribbutu ricanusciutu pi %2",
          MaxMacroSub1: "Passatu lu n\u00F9mmiru m\u00E0ssimu di sustituzzioni di macro di MathJax; c'\u00E8 quarchi chiamata a macro ricursiva?",
          MaxMacroSub2: "Passatu lu n\u00F9mmiru m\u00E0ssimu di sustituzzioni di MathJax; c'\u00E8 quarchi ambienti LaTeX ricursivu?",
          MissingArgFor: "Argumentu d\u00FB cumannu %1 ammancanti",
          ExtraAlignTab: "Tabbulazzioni d\u2019alliniamentu suverchia nt\u00F4 testu d\u00FB cumannu \\cases",
          BracketMustBeDimension: "L'argumentu tra par\u00E8ntisi pi %1 havi a \u00E8ssiri na diminzioni",
          InvalidEnv: "Nomu d'ambienti nun v\u00E0lidu \u00AB%1\u00BB",
          UnknownEnv: "Ambienti scanusciutu \u00AB%1\u00BB",
          ExtraCloseLooking: "Par\u00E8ntisi graffa chiusa suverchia quannu s'aspittava %1",
          MissingCloseBracket: "Nun s'attrova la \u00AB]\u00BB chiusa pi l'argumentu di %1",
          MissingOrUnrecognizedDelim: "Dilimitaturi pi %1 ammancanti o scanusciutu",
          MissingDimOrUnits: "Diminzioni o s\u00F2 unit\u00E0 ammancanti p\u00FB cumannu %1",
          TokenNotFoundForCommand: "Nun s'attrova lu s\u00ECmmulu %1 p\u00FB cumannu %2",
          MathNotTerminated: "Matim\u00E0tica nun cunchiusa nt\u00E2 buatta di testu",
          IllegalMacroParam: "Rifirimentu \u00F4n par\u00E0mitru di macro nun v\u00E0lidu",
          MaxBufferSize: "Passatu lu l\u00ECmiti di grannizza d\u00FB buffer nternu di MathJax; c'\u00E8 quarchi chiamata a macro ricursiva?",
          CommandNotAllowedInEnv: "Lu cumannu %1 nun \u00E8 cunzintutu nta l'ambienti %2",
          MultipleLabel: "Etichetta \u00AB%1\u00BB difinuta cchi\u00F9 voti",
          CommandAtTheBeginingOfLine: "%1 havi a stari \u00F4 principiu d\u00E2 riga",
          IllegalAlign: "Alliniamentu nun v\u00E0lidu spicificatu nt\u00F4 cumannu %1",
          BadMathStyleFor: "Stili matim\u00E0ticu nun v\u00E0lidu p\u00FB cumannu %1",
          PositiveIntegerArg: "L'argumentu di %1 havi a \u00E8ssiri nu nteru pusitivu",
          ErroneousNestingEq: "Annidamentu sbagghiatu d\u00EE strutturi d\u00E2 f\u00F2rmula",
          MultlineRowsOneCol: "Nta l\u2019ambienti %1 li righi hannu a aviri na culonna e una sula",
          MultipleBBoxProperty: "Prupit\u00E0 %1 spicificata dui voti nt\u00F4 cumannu %2",
          InvalidBBoxProperty: "\u00AB%1\u00BB nun pari un culuri, na diminzioni di m\u00E0rgini nternu, o nu stili",
          ExtraEndMissingBegin: "Cumannu %1 suverchiu o cumannu \\begingroup ammancanti",
          GlobalNotFollowedBy: "Cumannu %1 nun siguitu d\u00FB cumannu \\let, \\def, o \\newcommand",
          UndefinedColorModel: "Mudellu di culuri \u00AB%1\u00BB nun difinutu",
          ModelArg1: "Li valura di culuri p\u00FB mudellu %1 sunnu fatti di 3 n\u00F9mmira",
          InvalidDecimalNumber: "N\u00F9mmiru dicimali nun v\u00E0lidu",
          ModelArg2: "Li valura di culuri p\u00FB mudellu %1 hannu a \u00E8ssiri tra %2 e %3",
          InvalidNumber: "N\u00F9mmiru nun v\u00E0lidu",
          NewextarrowArg1: "Lu primu argumentu d\u00FB cumannu %1 havi a \u00E8ssiri lu nomu di na siquenza di cuntrollu",
          NewextarrowArg2: "Lu secunnu argumentu d\u00FB cumannu %1 hannu a \u00E8ssiri dui nteri spartuti di na v\u00ECrgula",
          NewextarrowArg3: "Lu terzu argumentu d\u00FB cumannu %1 havi a \u00E8ssiri nu n\u00F9mmiru di car\u00E0ttiri Unicode",
          NoClosingChar: "Ammanca la %1 di chiusura",
          IllegalControlSequenceName: "Nomu di siquenza di cuntrollu nun v\u00E0lidu p\u00FB cumannu %1",
          IllegalParamNumber: "N\u00F9mmiru di par\u00E0mitri nun v\u00E0lidu spicificatu nt\u00F4 cumannu %1",
          MissingCS: "Lu cumannu %1 havi a \u00E8ssiri siguitu di na siquenza di cuntrollu",
          CantUseHash2: "Usu nun cunzintutu d\u00FB car\u00E0ttiri # nt\u00F4 mudellu d\u00FB cumannu %1",
          SequentialParam: "Li par\u00E0mitri d\u00FB cumannu %1 hannu a \u00E8ssiri nummirati n siquenza",
          MissingReplacementString: "Stringa di rimpiazzu ammancanti nt\u00E2 difinizzioni d\u00FB cumannu %1",
          MismatchUseDef: "L'usu d\u00FB cumannu %1 nun currispunni \u00E2 s\u00F2 difinizzioni",
          RunawayArgument: "Argumentu d\u00FB cumannu %1 pirdutu?",
          NoClosingDelim: "Nun s'attrova lu dilimitaturi di chiusura p\u00FB cumannu %1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/scn/TeX.js");
