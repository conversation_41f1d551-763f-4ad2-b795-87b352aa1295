/*************************************************************
 *
 *  MathJax/localization/cs/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("cs","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "Na\u010D\u00EDt\u00E1 se web-font %1",
          CantLoadWebFont: "Nelze na\u010D\u00EDst web-font %1",
          FirefoxCantLoadWebFont: "Firefox nem\u016F\u017Ee na\u010D\u00EDtat web-fonty ze vzd\u00E1len\u00E9ho hostitele",
          CantFindFontUsing: "Nelze naj\u00EDt platn\u00FD font pomoc\u00ED %1",
          WebFontsNotAvailable: "Webov\u00E9 fonty nejsou k dispozici. M\u00EDsto toho jsou pou\u017Eity obr\u00E1zkov\u00E9 fonty."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/cs/HTML-CSS.js");
