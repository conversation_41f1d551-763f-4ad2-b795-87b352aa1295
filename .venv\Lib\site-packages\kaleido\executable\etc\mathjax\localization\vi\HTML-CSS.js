/*************************************************************
 *
 *  MathJax/localization/vi/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("vi","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "\u0110ang t\u1EA3i ph\u00F4ng ch\u1EEF Web %1",
          CantLoadWebFont: "Kh\u00F4ng th\u1EC3 t\u1EA3i ph\u00F4ng ch\u1EEF Web %1",
          FirefoxCantLoadWebFont: "Firefox kh\u00F4ng th\u1EC3 t\u1EA3i ph\u00F4ng ch\u1EEF Web t\u1EEB m\u1ED9t m\u00E1y ch\u1EE7 t\u1EEB xa",
          CantFindFontUsing: "Kh\u00F4ng t\u00ECm th\u1EA5y ph\u00F4ng ch\u1EEF h\u1EE3p l\u1EC7 d\u00F9ng %1",
          WebFontsNotAvailable: "Ph\u00F4ng ch\u1EEF Web kh\u00F4ng c\u00F3 s\u1EB5n; \u0111ang s\u1EED d\u1EE5ng ph\u00F4ng ch\u1EEF h\u00ECnh \u1EA3nh thay th\u1EBF"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/vi/HTML-CSS.js");
