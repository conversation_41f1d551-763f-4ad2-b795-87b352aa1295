import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  FormControlLabel,
  Switch,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Alert
} from '@mui/material';
import {
  Settings,
  ArrowBack,
  <PERSON>Forward,
  CheckCircle,
  Info,
  Tune
} from '@mui/icons-material';
import toast from 'react-hot-toast';

import { apiService } from '../services/apiService';

const DataProcessing = ({ onNext, onBack, setLoading, setError }) => {
  const [processingOptions, setProcessingOptions] = useState({
    cleaning_options: {
      remove_duplicates: true,
      handle_missing: 'impute',
      handle_outliers: 'cap',
      missing_threshold: 0.5
    },
    feature_engineering: {
      apply_feature_engineering: true,
      include_polynomial: false,
      feature_selection_k: 20
    }
  });
  
  const [processingResult, setProcessingResult] = useState(null);
  const [processingSuccess, setProcessingSuccess] = useState(false);

  const handleOptionChange = (category, option, value) => {
    setProcessingOptions(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [option]: value
      }
    }));
  };

  const processData = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await apiService.processData(processingOptions);
      setProcessingResult(result);
      
      if (result.success) {
        setProcessingSuccess(true);
        toast.success('Data processed successfully!');
      } else {
        toast.error('Data processing failed. Please check the issues.');
      }
    } catch (error) {
      setError(error.message);
      toast.error('Processing failed: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const renderProcessingOptions = () => {
    return (
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          <Settings sx={{ mr: 1, verticalAlign: 'middle' }} />
          Processing Options
        </Typography>
        
        <Grid container spacing={3}>
          {/* Data Cleaning Options */}
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom>
              Data Cleaning
            </Typography>
            
            <FormControlLabel
              control={
                <Switch
                  checked={processingOptions.cleaning_options.remove_duplicates}
                  onChange={(e) => handleOptionChange('cleaning_options', 'remove_duplicates', e.target.checked)}
                  color="primary"
                />
              }
              label="Remove Duplicate Rows"
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={processingOptions.cleaning_options.handle_outliers === 'cap'}
                  onChange={(e) => handleOptionChange(
                    'cleaning_options', 
                    'handle_outliers', 
                    e.target.checked ? 'cap' : 'none'
                  )}
                  color="primary"
                />
              }
              label="Handle Outliers (Cap at 3 IQR)"
            />
          </Grid>
          
          {/* Feature Engineering Options */}
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom>
              Feature Engineering
            </Typography>
            
            <FormControlLabel
              control={
                <Switch
                  checked={processingOptions.feature_engineering.apply_feature_engineering}
                  onChange={(e) => handleOptionChange('feature_engineering', 'apply_feature_engineering', e.target.checked)}
                  color="primary"
                />
              }
              label="Apply Feature Engineering"
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={processingOptions.feature_engineering.include_polynomial}
                  onChange={(e) => handleOptionChange('feature_engineering', 'include_polynomial', e.target.checked)}
                  color="primary"
                  disabled={!processingOptions.feature_engineering.apply_feature_engineering}
                />
              }
              label="Include Polynomial Features"
            />
          </Grid>
        </Grid>
      </Paper>
    );
  };

  const renderProcessingResult = () => {
    if (!processingResult) return null;

    const { processing_summary } = processingResult;
    
    return (
      <Paper sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          Processing Results
        </Typography>
        
        <Grid container spacing={3}>
          {/* Data Format */}
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom color="text.primary">
              Data Information
            </Typography>
            <List dense>
              <ListItem>
                <ListItemIcon>
                  <Info color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="Data Format"
                  secondary={processing_summary?.data_format || 'standard'}
                  primaryTypographyProps={{ color: 'text.primary' }}
                  secondaryTypographyProps={{ color: 'text.secondary' }}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Info color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="Original Shape"
                  secondary={processing_summary?.original_shape ?
                    `${processing_summary.original_shape[0]} rows × ${processing_summary.original_shape[1]} columns` :
                    'N/A'}
                  primaryTypographyProps={{ color: 'text.primary' }}
                  secondaryTypographyProps={{ color: 'text.secondary' }}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Info color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="Final Shape"
                  secondary={processing_summary?.final_shape ?
                    `${processing_summary.final_shape[0]} rows × ${processing_summary.final_shape[1]} columns` :
                    'N/A'}
                  primaryTypographyProps={{ color: 'text.primary' }}
                  secondaryTypographyProps={{ color: 'text.secondary' }}
                />
              </ListItem>
            </List>
          </Grid>
          
          {/* Feature Engineering */}
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom color="text.primary">
              Feature Engineering
            </Typography>
            {processing_summary?.feature_engineering_applied ? (
              <Alert severity="success" sx={{ mb: 2 }}>
                ✅ Feature engineering was successfully applied
                {processing_summary?.engineering_summary?.features_created && (
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    🎯 {processing_summary.engineering_summary.features_created} new features created
                  </Typography>
                )}
              </Alert>
            ) : (
              <Alert severity="info" sx={{ mb: 2 }}>
                ℹ️ No feature engineering was applied
              </Alert>
            )}

            {processing_summary?.engineering_summary && (
              <Box>
                <Typography variant="body2" gutterBottom color="text.primary">
                  <strong>Features Created:</strong> {processing_summary.engineering_summary.total_features_created || 0}
                </Typography>
                
                {processing_summary.engineering_summary.created_features && (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                    {processing_summary.engineering_summary.created_features.slice(0, 10).map((feature, index) => (
                      <Chip 
                        key={index} 
                        label={feature} 
                        size="small" 
                        variant="outlined"
                        color="primary"
                      />
                    ))}
                    {processing_summary.engineering_summary.created_features.length > 10 && (
                      <Chip 
                        label={`+${processing_summary.engineering_summary.created_features.length - 10} more`} 
                        size="small" 
                        variant="outlined"
                      />
                    )}
                  </Box>
                )}
              </Box>
            )}
          </Grid>
        </Grid>
        
        <Divider sx={{ my: 2 }} />
        
        {/* Available Columns */}
        {processing_summary?.columns_after_processing && (
          <Box>
            <Typography variant="subtitle1" gutterBottom color="text.primary">
              Available Columns After Processing
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {processing_summary.columns_after_processing.slice(0, 15).map((column, index) => (
                <Chip 
                  key={index} 
                  label={column} 
                  size="small" 
                  variant="outlined"
                />
              ))}
              {processing_summary.columns_after_processing.length > 15 && (
                <Chip 
                  label={`+${processing_summary.columns_after_processing.length - 15} more`} 
                  size="small" 
                  variant="outlined"
                  color="primary"
                />
              )}
            </Box>
          </Box>
        )}
        
        {/* Available Targets */}
        {processing_summary?.available_targets && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle1" gutterBottom color="text.primary">
              Available Target Variables
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {processing_summary.available_targets.map((target, index) => (
                <Chip 
                  key={index} 
                  label={target} 
                  size="small" 
                  color="secondary"
                />
              ))}
            </Box>
          </Box>
        )}
      </Paper>
    );
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Process & Clean Data
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Process and clean your data to prepare it for model training.
        This step handles missing values, outliers, and creates engineered features.
      </Typography>
      
      {renderProcessingOptions()}
      
      <Button 
        variant="contained" 
        color="primary" 
        onClick={processData}
        startIcon={<Tune />}
        sx={{ mb: 2 }}
      >
        Process Data
      </Button>
      
      {processingResult && renderProcessingResult()}
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
        <Button 
          variant="outlined" 
          onClick={onBack}
          startIcon={<ArrowBack />}
        >
          Back
        </Button>
        
        <Button 
          variant="contained" 
          color="primary" 
          onClick={onNext}
          endIcon={<ArrowForward />}
          disabled={!processingSuccess}
        >
          Continue to Feature Management
        </Button>
      </Box>
    </Box>
  );
};

export default DataProcessing;