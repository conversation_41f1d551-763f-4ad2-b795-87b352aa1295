/*************************************************************
 *
 *  MathJax/localization/ia/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ia","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "Adjuta de MathJax",
          MathJax: "*MathJax* es un bibliotheca in JavaScript que permitte al autores includer formulas mathematic in lor paginas web. Nulle action del lector es necessari pro facer isto functionar.",
          Browsers: "*Navigatores*: MathJax functiona con tote le navigatores web moderne como IE6+, Firefox 3+, Chrome 0.2+, Safari 2+, Opera 9.6+ e le major parte del navigatores in apparatos mobile.",
          Menu: "*Menu mathematic*: MathJax adde un menu contextual al equationes. Pro acceder a iste menu, clicca sur un formula premente le button dextre del mus o le clave CTRL.",
          ShowMath: "*Monstrar formula como* permitte revelar le codice-fonte del formula pro copiar e collar (in formato MathML or in su formato original).",
          Settings: "*Configuration* te da le controlo sur le functionalitate de MathJax, como le dimension del formulas, e le mechanismo usate pro presentar equationes.",
          Language: "*Lingua* permitte seliger le lingua usate per MathJax pro su menus e messages de aviso.",
          Zoom: "*Math Zoom*: Si vos ha difficultate a leger un equation, MathJax pote aggrandir lo pro facilitar le lectura.",
          Accessibilty: "*Accessibilitate*: MathJax functiona automaticamente con lectores de schermo pro render le formulas accessibile al personas qui vide mal.",
          Fonts: "*Typos de litteras*: MathJax usa certe typos de litteras mathematic si illos es installate in vostre computator; si non, illo usa typos de litteras obtenite per le web. Ben que non obligatori, le typos de litteras installate localmente rendera le composition plus rapide. Nos suggere installar le [typos de litteras STIX](%1)."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ia/HelpDialog.js");
