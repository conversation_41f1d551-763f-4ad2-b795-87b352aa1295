/*************************************************************
 *
 *  MathJax/localization/he/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("he","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "\u05E2\u05D6\u05E8\u05D4 \u05E9\u05DC MathJax",
          MathJax: "*MathJax* \u05D4\u05D9\u05D0 \u05E1\u05E4\u05E8\u05D9\u05D9\u05EA \u05D2'\u05D0\u05D5\u05D4 \u05E1\u05E7\u05E8\u05D9\u05E4\u05D8 \u05E9\u05DE\u05D0\u05E4\u05E9\u05E8\u05EA \u05DC\u05DB\u05D5\u05EA\u05D1\u05D9 \u05D0\u05EA\u05E8\u05D9\u05DD \u05DC\u05DB\u05DC\u05D5\u05DC \u05E0\u05D5\u05E1\u05D7\u05D0\u05D5\u05EA \u05DE\u05EA\u05DE\u05D8\u05D9\u05D5\u05EA \u05D1\u05D3\u05E4\u05D9\u05DD. \u05D4\u05E7\u05D5\u05E8\u05D0\u05D9\u05DD \u05D0\u05D9\u05E0\u05DD \u05E6\u05E8\u05D9\u05DB\u05D9\u05DD \u05DC\u05E2\u05E9\u05D5\u05EA \u05D3\u05D1\u05E8 \u05DB\u05D3\u05D9 \u05E9\u05D6\u05D4 \u05D9\u05E7\u05E8\u05D4.",
          Browsers: "*\u05D3\u05E4\u05D3\u05E4\u05E0\u05D9\u05DD*: MathJax \u05E2\u05D5\u05D1\u05D3 \u05E2\u05DD \u05DB\u05DC \u05D4\u05D3\u05E4\u05D3\u05E4\u05E0\u05D9\u05DD \u05D4\u05DE\u05D5\u05D3\u05E8\u05E0\u05D9\u05D9\u05DD, \u05DB\u05D5\u05DC\u05DC \u05D0\u05D9\u05E0\u05D8\u05E8\u05E0\u05D8 \u05D0\u05E7\u05E1\u05E4\u05DC\u05D5\u05E8\u05E8 \u05DE\u05D2\u05E8\u05E1\u05D4 6 \u05D5\u05DE\u05E2\u05DC\u05D4, \u05E4\u05D9\u05D9\u05E8\u05E4\u05D5\u05E7\u05E1 \u05DE\u05D2\u05E8\u05E1\u05D4 3 \u05D5\u05DE\u05E2\u05DC\u05D4, \u05DB\u05E8\u05D5\u05DD \u05DE\u05D2\u05E8\u05E1\u05D4 0.2 \u05D5\u05DE\u05E2\u05DC\u05D4, \u05E1\u05E4\u05D0\u05E8\u05D9 \u05DE\u05D2\u05E8\u05E1\u05D4 2 \u05D5\u05DE\u05E2\u05DC\u05D4, \u05D0\u05D5\u05E4\u05E8\u05D4 \u05DE\u05D2\u05E8\u05E1\u05D4 9.6 \u05D5\u05DE\u05E2\u05DC\u05D4 \u05D5\u05E8\u05D5\u05D1 \u05D4\u05D3\u05E4\u05D3\u05E4\u05E0\u05D9\u05DD \u05DC\u05DE\u05DB\u05E9\u05D9\u05E8\u05D9\u05DD \u05E0\u05D9\u05D9\u05D3\u05D9\u05DD.",
          Menu: "*\u05EA\u05E4\u05E8\u05D9\u05D8 \u05DE\u05EA\u05DE\u05D8\u05D9\u05E7\u05D4*: MathJax \u05DE\u05D5\u05E1\u05D9\u05E3 \u05EA\u05E4\u05E8\u05D9\u05D8 \u05D4\u05E7\u05E9\u05E8 \u05DC\u05DE\u05E9\u05D5\u05D5\u05D0\u05D5\u05EA. \u05D9\u05E9 \u05DC\u05E2\u05E9\u05D5\u05EA \u05DC\u05D7\u05D9\u05E6\u05D4 \u05D9\u05DE\u05E0\u05D9\u05EA \u05D0\u05D5 \u05DC\u05D7\u05D9\u05E6\u05D4 \u05E2\u05DD Ctrl \u05D1\u05DB\u05DC \u05E0\u05D5\u05E1\u05D7\u05D4 \u05DB\u05D3\u05D9 \u05DC\u05D4\u05D9\u05DB\u05E0\u05E1 \u05DC\u05EA\u05E4\u05E8\u05D9\u05D8.",
          ShowMath: "*\u05DC\u05D4\u05E6\u05D9\u05D2 \u05E0\u05D5\u05E1\u05D7\u05D0\u05D5\u05EA \u05D1\u05EA\u05D5\u05E8* \u05DE\u05D0\u05E4\u05E9\u05E8 \u05DC\u05DA \u05DC\u05E8\u05D0\u05D5\u05EA \u05D0\u05EA \u05E7\u05D5\u05D3 \u05D4\u05DE\u05E7\u05D5\u05E8 \u05E9\u05DC \u05D4\u05E0\u05D5\u05E1\u05D7\u05D4 \u05DC\u05D4\u05E2\u05EA\u05E7\u05D4 \u05D5\u05D4\u05D3\u05D1\u05E7\u05D4 (\u05D1\u05EA\u05D5\u05E8 MathML \u05D0\u05D5 \u05D1\u05E6\u05D5\u05E8\u05D4 \u05D4\u05DE\u05E7\u05D5\u05E8\u05D9\u05EA).",
          Settings: "*\u05D4\u05D2\u05D3\u05E8\u05D5\u05EA* \u05E0\u05D5\u05EA\u05E0\u05D5\u05EA \u05DC\u05DA \u05E9\u05DC\u05D9\u05D8\u05D4 \u05E2\u05DC \u05D9\u05DB\u05D5\u05DC\u05D5\u05EA \u05E9\u05DC MathJax, \u05DB\u05D2\u05D5\u05DF \u05D4\u05D2\u05D5\u05D3\u05DC \u05E9\u05DC \u05D4\u05E0\u05D5\u05E1\u05D7\u05D0\u05D5\u05EA \u05D5\u05D4\u05E9\u05D9\u05D8\u05D4 \u05DC\u05D4\u05E6\u05D2\u05EA \u05D4\u05DE\u05E9\u05D5\u05D5\u05D0\u05D5\u05EA.",
          Language: "*\u05E9\u05E4\u05D4* \u2013 \u05DB\u05D0\u05DF \u05D0\u05E4\u05E9\u05E8 \u05DC\u05D1\u05D7\u05D5\u05E8 \u05D0\u05EA \u05D4\u05E9\u05E4\u05D4 \u05E9\u05BEMathJax \u05DE\u05E6\u05D9\u05D2\u05D4 \u05D1\u05EA\u05E4\u05E8\u05D9\u05D8\u05D9\u05DD \u05D5\u05D1\u05D4\u05D5\u05D3\u05E2\u05D5\u05EA \u05D4\u05D0\u05D6\u05D4\u05E8\u05D4",
          Zoom: "*\u05E7\u05D9\u05E8\u05D5\u05D1 \u05DE\u05EA\u05DE\u05D8\u05D9\u05E7\u05D4*: \u05D0\u05DD \u05E7\u05E9\u05D4 \u05DC\u05DA \u05DC\u05E7\u05E8\u05D5\u05D0 \u05DE\u05E9\u05D5\u05D5\u05D0\u05D4, MathJax \u05D9\u05DB\u05D5\u05DC \u05DC\u05D4\u05D2\u05D3\u05D9\u05DC \u05D0\u05D5\u05EA\u05D4 \u05DB\u05D3\u05D9 \u05DC\u05E2\u05D6\u05D5\u05E8 \u05DC\u05DA \u05DC\u05E8\u05D0\u05D5\u05EA \u05D0\u05D5\u05EA\u05D4 \u05D8\u05D5\u05D1 \u05D9\u05D5\u05EA\u05E8.",
          Accessibilty: "*\u05E0\u05D2\u05D9\u05E9\u05D5\u05EA*: MathJax \u05E2\u05D5\u05D1\u05D3 \u05D0\u05D5\u05D8\u05D5\u05DE\u05D8\u05D9\u05EA \u05E2\u05DD \u05E7\u05D5\u05E8\u05D0\u05D9 \u05DE\u05E1\u05DA \u05DB\u05D3\u05D9 \u05DC\u05D5\u05D5\u05D3\u05D0 \u05E9\u05D4\u05E0\u05D5\u05E1\u05D7\u05D0\u05D5\u05EA \u05E0\u05D2\u05D9\u05E9\u05D5\u05EA \u05DC\u05DB\u05D1\u05D3\u05D9 \u05E8\u05D0\u05D9\u05D9\u05D4.",
          Fonts: "*\u05D2\u05D5\u05E4\u05E0\u05D9\u05DD*: MathJax \u05DE\u05E9\u05EA\u05DE\u05E9 \u05D1\u05D2\u05D5\u05E4\u05E0\u05D9\u05DD \u05DE\u05EA\u05DE\u05D8\u05D9\u05D9\u05DD \u05DE\u05E1\u05D5\u05D9\u05DE\u05D9\u05DD \u05D0\u05DD \u05D4\u05DD \u05DE\u05D5\u05EA\u05E7\u05E0\u05D9\u05DD \u05D1\u05DE\u05D7\u05E9\u05D1 \u05E9\u05DC\u05DA; \u05D0\u05D7\u05E8\u05EA, \u05D4\u05D5\u05D0 \u05DE\u05E9\u05EA\u05DE\u05E9 \u05D1\u05D2\u05D5\u05E4\u05E0\u05D9 \u05E8\u05E9\u05EA. \u05D0\u05E3 \u05E9\u05D6\u05D4 \u05D0\u05D9\u05E0\u05D5 \u05E0\u05D7\u05D5\u05E5, \u05D2\u05D5\u05E4\u05E0\u05D9\u05DD \u05DE\u05D5\u05EA\u05E7\u05E0\u05D9\u05DD \u05DE\u05E7\u05D5\u05DE\u05D9\u05EA \u05D9\u05DB\u05D5\u05DC \u05DC\u05D6\u05E8\u05D6 \u05D0\u05EA \u05D4\u05E1\u05D3\u05B7\u05E8. \u05D0\u05E0\u05D7\u05E0\u05D5 \u05DE\u05DE\u05DC\u05D9\u05E6\u05D9\u05DD \u05DC\u05D4\u05EA\u05E7\u05D9\u05DF \u05D0\u05EA [\u05D2\u05D5\u05E4\u05E0\u05D9 STIX](%1).",
          CloseDialog: "\u05E1\u05D2\u05D9\u05E8\u05EA \u05D7\u05DC\u05D5\u05DF \u05D3\u05D5\u05BE\u05E9\u05D9\u05D7 \u05E2\u05D6\u05E8\u05D4"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/he/HelpDialog.js");
