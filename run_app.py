#!/usr/bin/env python3
"""
Simple HTML interface runner for Pump Time Model.
Starts both backend API and simple HTML frontend UI server in one script.
For the full React interface, use start_app.py instead.
"""

import threading
import time
import webbrowser
import os
import sys
import signal
import subprocess
from multiprocessing import Process
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_backend():
    """Run the Flask backend API"""
    try:
        logger.info("🚀 Starting Backend API on port 5000...")
        
        # Import and run the Flask app
        from app import app
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
        
    except Exception as e:
        logger.error(f"❌ Backend failed to start: {e}")

def run_frontend():
    """Run the frontend UI server"""
    try:
        # Wait a moment for backend to start
        time.sleep(3)
        
        logger.info("🌐 Starting Frontend UI Server on port 5060...")

        # Import and run the UI server
        import http.server
        import socketserver
        from urllib.parse import urlparse

        class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
            """HTTP Request Handler with CORS support"""

            def end_headers(self):
                """Add CORS headers to all responses"""
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                super().end_headers()

            def do_OPTIONS(self):
                """Handle preflight OPTIONS requests"""
                self.send_response(200)
                self.end_headers()

            def do_GET(self):
                """Handle GET requests"""
                if self.path == '/' or self.path == '/index.html':
                    # Serve the main UI
                    self.path = '/simple_ui.html'

                # Check if file exists
                file_path = self.path.lstrip('/')
                if os.path.exists(file_path):
                    super().do_GET()
                else:
                    # Return 404 for missing files
                    self.send_error(404, f"File not found: {self.path}")

            def log_message(self, format, *args):
                """Custom log format"""
                logger.info(f"[UI Server] {format % args}")

        # Change to the directory containing the HTML file
        os.chdir(os.path.dirname(os.path.abspath(__file__)))

        # Check if simple_ui.html exists
        if not os.path.exists('simple_ui.html'):
            logger.error("❌ simple_ui.html not found in current directory")
            return

        # Create and start server
        with socketserver.TCPServer(("", 5060), CORSHTTPRequestHandler) as httpd:
            logger.info("✅ Frontend UI Server started successfully!")
            httpd.serve_forever()
            
    except Exception as e:
        logger.error(f"❌ Frontend failed to start: {e}")

def check_backend_status():
    """Check if the backend API is running"""
    import urllib.request
    try:
        response = urllib.request.urlopen('http://localhost:5000/api/status', timeout=5)
        return response.getcode() == 200
    except:
        return False

def open_browser_when_ready():
    """Open browser once both services are ready"""
    max_attempts = 30
    attempt = 0
    
    while attempt < max_attempts:
        if check_backend_status():
            logger.info("🌐 Opening application in browser...")
            webbrowser.open('http://localhost:5060')
            break
        
        time.sleep(2)
        attempt += 1
        logger.info(f"⏳ Waiting for services to start... ({attempt}/{max_attempts})")
    
    if attempt >= max_attempts:
        logger.warning("⚠️ Services took too long to start. Please check manually.")

def signal_handler(signum, frame):
    """Handle Ctrl+C gracefully"""
    logger.info("\n🛑 Shutting down services...")
    os._exit(0)

def main():
    """Main function to start both services"""
    
    print("🚢 Evos Pump Time Prediction - Simple HTML Interface")
    print("=" * 60)
    print("🚀 Starting Backend API (port 5000)")
    print("🌐 Starting Simple HTML Frontend UI (port 5060)")
    print("📱 Will open browser automatically when ready")
    print("=" * 60)
    print("ℹ️  This serves the simple HTML interface (simple_ui.html)")
    print("ℹ️  For the full React interface, use: python start_app.py")
    print("💡 Press Ctrl+C to stop both services")
    print("🔗 Let's Evolve Together - Evos")
    print("=" * 60)
    
    # Set up signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # Start backend in a separate process
        backend_process = Process(target=run_backend)
        backend_process.daemon = True
        backend_process.start()
        
        # Start browser opener in a separate thread
        browser_thread = threading.Thread(target=open_browser_when_ready)
        browser_thread.daemon = True
        browser_thread.start()
        
        # Run frontend in main thread (this will block)
        run_frontend()
        
    except KeyboardInterrupt:
        logger.info("\n🛑 Received shutdown signal")
    except Exception as e:
        logger.error(f"❌ Application error: {e}")
    finally:
        logger.info("🔄 Cleaning up...")
        if 'backend_process' in locals():
            backend_process.terminate()
        logger.info("✅ Shutdown complete")

if __name__ == "__main__":
    main()
