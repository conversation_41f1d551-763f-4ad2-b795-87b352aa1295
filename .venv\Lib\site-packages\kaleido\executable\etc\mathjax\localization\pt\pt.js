/*************************************************************
 *
 *  MathJax/localization/pt/pt.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("pt",null,{
  menuTitle: "portugu\u00EAs",
  version: "2.7.5",
  isLoaded: true,
  domains: {
    "_": {
        version: "2.7.5",
        isLoaded: true,
        strings: {
          MathProcessingError: "Erro no processamento das f\u00F3rmulas",
          MathError: "Erro de matem\u00E1tica",
          LoadFile: "A carregar %1",
          Loading: "A carregar",
          LoadFailed: "O ficheiro n\u00E3o pode ser carregado: %1",
          ProcessMath: "A processar f\u00F3rmula: %1%%",
          Processing: "A processar",
          TypesetMath: "A formatar f\u00F3rmulas: %1%%",
          Typesetting: "A formatar",
          MathJaxNotSupported: "O seu navegador n\u00E3o suporta MathJax",
          ErrorTips: "Dicas de depura\u00E7\u00E3o: use %%1, para inspecionar %%2 no console do navegador"
        }
    },
    "FontWarnings": {},
    "HTML-CSS": {},
    "HelpDialog": {},
    "MathML": {},
    "MathMenu": {},
    "TeX": {}
  },
  plural: function (n) {
      if (n === 1) {return 1} // one
      return 2; // other
    },
  number: function (n) {
      return String(n).replace(".", ","); // replace dot by comma
    }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/pt/pt.js");
