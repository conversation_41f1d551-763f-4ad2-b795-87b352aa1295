/*************************************************************
 *
 *  MathJax/localization/nl/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("nl","MathMenu",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Show: "Wiskunde weergeven als",
          MathMLcode: "MathML-code",
          OriginalMathML: "Oorspronkelijke MathML",
          TeXCommands: "TeX-commando's",
          AsciiMathInput: "AsciiMathML-invoer",
          Original: "Oorspronkelijke vorm",
          ErrorMessage: "Foutmelding",
          Annotation: "Annotatie",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "Inhoud MathML",
          OpenMath: "OpenMath",
          texHints: "TeX-hints weergeven in MathML",
          Settings: "Instellingen voor wiskundige formules",
          ZoomTrigger: "Inzoomen als",
          Hover: "Aanwijseffect",
          Click: "Klik",
          DoubleClick: "Dubbelklik",
          NoZoom: "Niet zoomen",
          TriggerRequires: "Trigger vereist:",
          Option: "Optie",
          Alt: "Alt",
          Command: "Command",
          Control: "Control",
          Shift: "Shift",
          ZoomFactor: "Zoomfactor",
          Renderer: "Mathverwerking",
          MPHandles: "MathPlayer de volgende gebeurtenissen laten afhandelen:",
          MenuEvents: "Menugebeurtenissen",
          MouseEvents: "Muisgebeurtenissen",
          MenuAndMouse: "Muis- en menugebeurtenissen",
          FontPrefs: "Lettertypevoorkeuren",
          ForHTMLCSS: "Voor HTML-CSS:",
          Auto: "Automatisch",
          TeXLocal: "TeX (lokaal)",
          TeXWeb: "TeX (web)",
          TeXImage: "TeX (afbeelding)",
          STIXLocal: "STIX (lokaal)",
          STIXWeb: "STIX (web)",
          AsanaMathWeb: "Asana Math (web)",
          GyrePagellaWeb: "Gyre Pagella (web)",
          GyreTermesWeb: "Gyre Termes (web)",
          LatinModernWeb: "Latin Modern (web)",
          NeoEulerWeb: "Neo Euler (web)",
          ContextMenu: "Contextueel menu",
          Browser: "Browser",
          Scale: "Alle wiskunde schalen als ...",
          Discoverable: "Uitlichten bij aanwijzen",
          Locale: "Taal",
          LoadLocale: "Laden van URL ...",
          About: "Over MathJax",
          Help: "MathJax-hulp",
          localTeXfonts: "met behulp van lokale TeX-lettertypen",
          webTeXfonts: "met behulp van weblettertype TeX",
          imagefonts: "met behulp van afbeeldingslettertypes",
          localSTIXfonts: "met behulp van lokale STIX-lettertypes",
          webSVGfonts: "met behulp van SVG-weblettertypen",
          genericfonts: "met behulp van standaard Unicodelettertypen",
          wofforotffonts: "WOFF- of OTF-lettertypen",
          eotffonts: "EOT-lettertypen",
          svgfonts: "SVG-lettertypen",
          WebkitNativeMMLWarning: "Uw browser lijkt MathML niet te ondersteunen. Door te kiezen voor MathML-uitvoer kunnen de wiskundige formules op de pagina onleesbaar worden.",
          MSIENativeMMLWarning: "Internet Explorer vereist de MathPlayer plug-in om MathML-uitvoer te verwerken",
          OperaNativeMMLWarning: "De ondersteuning voor MathML in Opera is beperkt, dus als u overschakelt naar weergave via MathML kunnen sommige expressies slecht worden weergegeven.",
          SafariNativeMMLWarning: "De in uw browser ingebouwde MathML heeft niet alle mogelijkheden die door MathJax worden gebruikt, dus niet alle expressies kunnen correct worden weergegeven.",
          FirefoxNativeMMLWarning: "De in uw browser ingebouwde MathML heeft niet alle mogelijkheden die door MathJax worden gebruikt, dus niet alle expressies kunnen correct worden weergegeven.",
          MSIESVGWarning: "SVG is niet ge\u00EFmplementeerd in Internet Explorer versies v\u00F3\u00F3r IE9 en ook niet wanneer het IE8 of lager emuleert. Kiezen voor SVG-uitvoer veroorzaakt onjuiste weergave van de wiskundige formules.",
          LoadURL: "Vertalingsdata van deze URL laden:",
          BadURL: "De URL moet naar een JavaScriptbestand wijzen waarin vertaalgegevens voor MathJax staan. De naam van het JavaScriptbestand moet eindigen op \".js\".",
          BadData: "Vertalingsdata laden van %1 niet gelukt",
          SwitchAnyway: "Evengoed de weergever wijzigen?\n\nDruk op OK om te wijzigen, ANNULEREN om door te gaan met de huidige wijze van weergeven.",
          ScaleMath: "Alle wiskundige formules schalen (in vergelijking tot de omliggende tekst) met",
          NonZeroScale: "De schaal hoort geen nul te zijn",
          PercentScale: "De schaal moet een percentage zijn (bijvoorbeeld 120%%)",
          IE8warning: "Hierdoor wordt het MathJaxmenu en zoomfuncties uitgeschakeld, maar u kunt via Alt-klik op een expressie het MathJamenu weergeven.\n\nWilt u inderdaad de instellingen van MathPlayer wijzigen?",
          IE9warning: "Het context menu van MathJax wordt uitgeschakeld, maar u kunt in plaats daarvan door Alt-klikken op een expressie het MathJax menu weergeven.",
          NoOriginalForm: "Geen oorspronkelijke vorm beschikbaar",
          Close: "Sluiten",
          EqSource: "MathJax Vergelijking Bron",
          CloseAboutDialog: "Over Mathjax sluiten",
          FastPreview: "Snell voorvertoning",
          AssistiveMML: "Assistieve MathML",
          InTabOrder: "Opnemen in tabvolgorde"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/nl/MathMenu.js");
