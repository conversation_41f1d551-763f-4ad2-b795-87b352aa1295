/*************************************************************
 *
 *  MathJax/localization/de/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("de","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "MathJax Hilfe",
          MathJax: "*MathJax* ist eine JavaScript-Bibliothek, die es Seitenautoren erm\u00F6glicht, mathematische Inhalte in ihre Webseiten einzubinden. Als Leser muss du nichts unternehmen, um das zu erreichen.",
          Browsers: "*Browser*: MathJax funktioniert mit allen modernen Browsern inklusive Internet Explorer 6+, Firefox 3+, Chrome 0.2+, Safari 2+, Opera 9.6+ und den meisten mobilen Browsern.",
          Menu: "*Men\u00FC*: MathJax f\u00FCgt bei Gleichungen ein Kontextmen\u00FC hinzu. Um das Men\u00FC anzuzeigen, klicke mit der rechten Maustaste oder halte beim Klicken die Strg-Taste gedr\u00FCckt.",
          ShowMath: "*Inhalt zeigen als* erlaubt dir, das Formelquellenmarkup f\u00FCr Kopieren \u0026 Einf\u00FCgen anzusehen (als MathML oder im Originalformat).",
          Settings: "*Einstellungen* gibt dir die Kontrolle \u00FCber MathJax-Funktionen, wie die Inhaltsgr\u00F6\u00DFe und den verwendeten Mechanismus zum Anzeigen von Gleichungen.",
          Language: "*Sprache* l\u00E4sst dir die Sprache ausw\u00E4hlen, die von MathJax f\u00FCr die Men\u00FCs und Warnmeldungen verwendet wird.",
          Zoom: "*Zoom*: Wenn du beim Lesen einer Gleichung Schwierigkeiten hast, kann MathJax sie vergr\u00F6\u00DFern, damit du sie besser sehen kannst.",
          Accessibilty: "*Barrierefreiheit*: MathJax funktioniert automatisch mit Screenreadern, um die Inhalte f\u00FCr Sehbehinderte verf\u00FCgbar zu machen.",
          Fonts: "*Schriftarten*: MathJax verwendet bestimmte Schriften, wenn sie auf deinem Computer installiert sind. Anderenfalls werden webbasierte Schriften verwendet. Obwohl das nicht erforderlich ist, beschleunigen lokal installierte Schriften die Schriftsetzung. Wir empfehlen die Installation der [STIX-Schriften](%1).",
          CloseDialog: "Hilfedialog schlie\u00DFen"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/de/HelpDialog.js");
