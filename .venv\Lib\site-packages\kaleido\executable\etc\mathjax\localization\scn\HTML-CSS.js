/*************************************************************
 *
 *  MathJax/localization/scn/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("scn","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "Scarricamentu d\u00FB tipu di car\u00E0ttiri web %1",
          CantLoadWebFont: "Nun si potti scarricari lu tipu di car\u00E0ttiri web %1",
          FirefoxCantLoadWebFont: "Firefox nun p\u00F2 scarricari tipi di car\u00E0ttiri web di n'host rimotu",
          CantFindFontUsing: "Nun si potti attruvari nu tipu di car\u00E0ttiri bonu tra %1",
          WebFontsNotAvailable: "Li tipi di car\u00E0ttiri web nun sunnu dispun\u00ECbbili, \u00F4 s\u00F2 postu s'ad\u00F2piranu mm\u00E0ggini fatti a tipu di car\u00E0ttiri"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/scn/HTML-CSS.js");
