<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evos Pump Time Prediction - Simple Interface</title>
    <link href="https://fonts.googleapis.com/css2?family=Blinker:wght@100;200;300;400;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Blinker', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .evos-header {
            background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }
        .evos-logo {
            height: 50px;
            margin-bottom: 10px;
            filter: brightness(0) invert(1);
        }
        .evos-tagline {
            font-size: 16px;
            font-weight: 300;
            opacity: 0.9;
            margin-top: 5px;
        }
        h1 {
            color: white;
            margin: 0;
            font-weight: 600;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #FF6B35;
            padding-bottom: 10px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #FF6B35;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
            color: white;
            border: none;
            cursor: pointer;
            margin: 10px 0;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: linear-gradient(135deg, #E55A2B 0%, #FF7043 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }
        .result {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #ffebee;
            border: 1px solid #f44336;
            color: #d32f2f;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .status-online { background-color: #4caf50; }
        .status-offline { background-color: #f44336; }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <!-- Evos Header -->
    <div class="evos-header">
        <img src="frontend/src/logo.png" alt="Evos Logo" class="evos-logo" onerror="this.style.display='none'">
        <h1>Evos Pump Time Prediction</h1>
        <div class="evos-tagline">Let's Evolve Together</div>
    </div>
    
    <!-- Status Section -->
    <div class="container">
        <h2>📊 System Status</h2>
        <div id="status-display">
            <div class="status">
                <span>🔗 API Connection</span>
                <span id="api-status">
                    <span class="status-indicator status-offline"></span>
                    Checking...
                </span>
            </div>
            <button onclick="checkStatus()">🔄 Refresh Status</button>
        </div>
        <div id="status-details"></div>
    </div>

    <!-- File Upload Section -->
    <div class="container">
        <h2>📁 Upload Data</h2>
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <p>📤 Click here to upload Excel file</p>
            <p style="color: #666; font-size: 14px;">Supported formats: .xlsx, .xls (Max 16MB)</p>
        </div>
        <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;" onchange="uploadFile()">
        <div id="upload-result"></div>
    </div>

    <!-- Sample Data Section -->
    <div class="container">
        <h2>🎲 Generate Sample Data</h2>
        <p>Don't have data? Generate sample vessel handling data for testing:</p>
        <button onclick="generateSampleData()">🎯 Generate Sample Data</button>
        <div id="sample-result"></div>
    </div>

    <!-- Model Training Section -->
    <div class="container">
        <h2>🤖 Train Model</h2>
        <div class="form-group">
            <label for="targetColumn">Target Variable:</label>
            <select id="targetColumn">
                <option value="pump_time">Pump Time</option>
                <option value="pre_pump_time">Pre-pump Time</option>
                <option value="post_pump_time">Post-pump Time</option>
            </select>
        </div>
        <button onclick="trainModel()">🚀 Train Models</button>
        <div id="training-result"></div>
    </div>

    <!-- Prediction Section -->
    <div class="container">
        <h2>🔮 Make Prediction</h2>
        <div class="form-group">
            <label for="operationType">Operation Type:</label>
            <select id="operationType">
                <option value="Loading">Loading</option>
                <option value="Discharging">Discharging</option>
            </select>
        </div>
        <div class="form-group">
            <label for="vesselType">Vessel Type:</label>
            <select id="vesselType">
                <option value="Vessel">Vessel</option>
                <option value="Barge">Barge</option>
            </select>
        </div>
        <div class="form-group">
            <label for="productType">Product Type:</label>
            <input type="text" id="productType" placeholder="e.g., Gasoline, Diesel, Crude Oil" value="Gasoline">
        </div>
        <div class="form-group">
            <label for="productQuantity">Product Quantity:</label>
            <input type="number" id="productQuantity" placeholder="e.g., 15000" value="15000">
        </div>
        <button onclick="makePrediction()">🎯 Predict Time</button>
        <div id="prediction-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';

        // Check API status
        async function checkStatus() {
            try {
                const response = await fetch(`${API_BASE}/api/status`);
                const data = await response.json();
                
                document.getElementById('api-status').innerHTML = `
                    <span class="status-indicator status-online"></span>
                    Connected
                `;
                
                document.getElementById('status-details').innerHTML = `
                    <div class="result">
                        <h3>📈 Application Status</h3>
                        <p><strong>Data Loaded:</strong> ${data.data_loaded ? '✅ Yes' : '❌ No'}</p>
                        <p><strong>Model Trained:</strong> ${data.model_trained ? '✅ Yes' : '❌ No'}</p>
                        ${data.best_model ? `<p><strong>Best Model:</strong> ${data.best_model}</p>` : ''}
                        ${data.data_shape ? `<p><strong>Data Shape:</strong> ${data.data_shape[0]} rows × ${data.data_shape[1]} columns</p>` : ''}
                    </div>
                `;
            } catch (error) {
                document.getElementById('api-status').innerHTML = `
                    <span class="status-indicator status-offline"></span>
                    Disconnected
                `;
                document.getElementById('status-details').innerHTML = `
                    <div class="error">
                        <strong>❌ Connection Error:</strong> ${error.message}<br>
                        Make sure the backend is running on port 5000.
                    </div>
                `;
            }
        }

        // Upload file
        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) return;
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                const response = await fetch(`${API_BASE}/api/upload`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('upload-result').innerHTML = `
                        <div class="result">
                            <h3>✅ Upload Successful!</h3>
                            <p><strong>File:</strong> ${data.filename}</p>
                            <p><strong>Shape:</strong> ${data.shape[0]} rows × ${data.shape[1]} columns</p>
                            <p><strong>Columns:</strong> ${data.columns.join(', ')}</p>
                        </div>
                    `;
                } else {
                    throw new Error(data.error || 'Upload failed');
                }
            } catch (error) {
                document.getElementById('upload-result').innerHTML = `
                    <div class="error">
                        <strong>❌ Upload Error:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Generate sample data
        async function generateSampleData() {
            try {
                const response = await fetch(`${API_BASE}/api/sample-data`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('sample-result').innerHTML = `
                        <div class="result">
                            <h3>✅ Sample Data Generated!</h3>
                            <p><strong>Shape:</strong> ${data.shape[0]} rows × ${data.shape[1]} columns</p>
                            <p><strong>Columns:</strong> ${data.columns.join(', ')}</p>
                        </div>
                    `;
                } else {
                    throw new Error(data.error || 'Sample data generation failed');
                }
            } catch (error) {
                document.getElementById('sample-result').innerHTML = `
                    <div class="error">
                        <strong>❌ Error:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Train model
        async function trainModel() {
            const targetColumn = document.getElementById('targetColumn').value;
            
            try {
                document.getElementById('training-result').innerHTML = `
                    <div class="result">
                        <p>🔄 Training models... This may take a few minutes.</p>
                    </div>
                `;
                
                const response = await fetch(`${API_BASE}/api/train`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        target_column: targetColumn,
                        algorithms: ['linear_regression', 'random_forest'],
                        hyperparameter_tuning: false
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    let resultsHtml = `
                        <div class="result">
                            <h3>✅ Training Complete!</h3>
                            <p><strong>Best Model:</strong> ${data.best_model}</p>
                            <h4>📊 Model Performance:</h4>
                    `;
                    
                    for (const [model, metrics] of Object.entries(data.evaluation_results)) {
                        if (!metrics.error) {
                            resultsHtml += `
                                <p><strong>${model}:</strong> 
                                RMSE: ${metrics.rmse.toFixed(2)}, 
                                R²: ${metrics.r2.toFixed(3)}, 
                                MAE: ${metrics.mae.toFixed(2)}</p>
                            `;
                        }
                    }
                    
                    resultsHtml += `</div>`;
                    document.getElementById('training-result').innerHTML = resultsHtml;
                } else {
                    throw new Error(data.error || 'Training failed');
                }
            } catch (error) {
                document.getElementById('training-result').innerHTML = `
                    <div class="error">
                        <strong>❌ Training Error:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Make prediction
        async function makePrediction() {
            const inputData = {
                operation_type: document.getElementById('operationType').value,
                vessel_type: document.getElementById('vesselType').value,
                product_type: document.getElementById('productType').value,
                product_quantity: parseFloat(document.getElementById('productQuantity').value)
            };
            
            try {
                const response = await fetch(`${API_BASE}/api/predict`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        input_data: inputData
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const result = data.prediction_result;
                    document.getElementById('prediction-result').innerHTML = `
                        <div class="result">
                            <h3>🎯 Prediction Result</h3>
                            <p><strong>Predicted Time:</strong> ${result.prediction.toFixed(1)} minutes</p>
                            <p><strong>Model Used:</strong> ${result.model_used}</p>
                            <h4>📝 Input Details:</h4>
                            <pre>${JSON.stringify(result.input_features, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error(data.error || 'Prediction failed');
                }
            } catch (error) {
                document.getElementById('prediction-result').innerHTML = `
                    <div class="error">
                        <strong>❌ Prediction Error:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Initialize
        window.onload = function() {
            checkStatus();
        };
    </script>
</body>
</html>
