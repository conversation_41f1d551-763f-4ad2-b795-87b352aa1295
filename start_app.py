#!/usr/bin/env python3
"""
Start script for the Pump Time Model application with React frontend.
This script starts both the Flask backend and the full React frontend.
For the simple HTML interface, use run_app.py instead.
"""

import os
import sys
import subprocess
import time
import webbrowser
import signal
import platform

# Configuration
BACKEND_PORT = 5000
FRONTEND_PORT = 5050
OPEN_BROWSER = True

# Colors for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

# Check Python version
if sys.version_info < (3, 8):
    print(f"{Colors.RED}Error: Python 3.8 or higher is required.{Colors.ENDC}")
    sys.exit(1)

# Check if we're on Windows
IS_WINDOWS = platform.system() == "Windows"

def print_header():
    """Print application header"""
    print(f"\n{Colors.HEADER}{Colors.BOLD}{'=' * 60}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}🚢 Evos Pump Time Prediction Application{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}Let's Evolve Together{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{'=' * 60}{Colors.ENDC}\n")

def check_requirements():
    """Check if all requirements are installed"""
    print(f"{Colors.BLUE}Checking requirements...{Colors.ENDC}")
    
    # Check if requirements.txt exists
    if not os.path.exists('requirements.txt'):
        print(f"{Colors.RED}Error: requirements.txt not found.{Colors.ENDC}")
        sys.exit(1)
    
    # Check if Node.js is installed
    try:
        subprocess.run(
            ['node', '--version'], 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE, 
            check=True
        )
    except (subprocess.SubprocessError, FileNotFoundError):
        print(f"{Colors.RED}Error: Node.js is not installed or not in PATH.{Colors.ENDC}")
        print(f"{Colors.YELLOW}Please install Node.js 16 or higher: https://nodejs.org/{Colors.ENDC}")
        sys.exit(1)
    
    # Check if frontend directory exists
    if not os.path.exists('frontend'):
        print(f"{Colors.RED}Error: frontend directory not found.{Colors.ENDC}")
        sys.exit(1)
    
    # Check if app.py exists
    if not os.path.exists('app.py'):
        print(f"{Colors.RED}Error: app.py not found.{Colors.ENDC}")
        sys.exit(1)
    
    print(f"{Colors.GREEN}All requirements satisfied!{Colors.ENDC}")

def install_dependencies():
    """Install Python and Node.js dependencies if needed"""
    print(f"{Colors.BLUE}Checking dependencies...{Colors.ENDC}")
    
    # Install Python dependencies if needed
    try:
        print(f"{Colors.YELLOW}Installing Python dependencies...{Colors.ENDC}")
        result = subprocess.run(
            [sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'],
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            # Check if it's just optional dependencies that failed
            if "shap" in result.stderr and "Microsoft Visual C++" in result.stderr:
                print(f"{Colors.YELLOW}Warning: Optional dependency 'shap' could not be installed.{Colors.ENDC}")
                print(f"{Colors.YELLOW}This requires Microsoft Visual C++ Build Tools on Windows.{Colors.ENDC}")
                print(f"{Colors.YELLOW}The application will continue without model explainability features.{Colors.ENDC}")
                
                # Install core dependencies without shap
                print(f"{Colors.YELLOW}Installing core dependencies...{Colors.ENDC}")
                try:
                    # Check if core dependencies are installed
                    subprocess.run(
                        [sys.executable, '-c', 'import flask, pandas, numpy, scikit-learn'],
                        check=True
                    )
                    print(f"{Colors.GREEN}Core dependencies are available.{Colors.ENDC}")
                except subprocess.SubprocessError:
                    print(f"{Colors.RED}Error: Core dependencies are missing.{Colors.ENDC}")
                    print(f"{Colors.RED}Please install them manually: pip install flask pandas numpy scikit-learn{Colors.ENDC}")
                    sys.exit(1)
            else:
                print(f"{Colors.RED}Error installing Python dependencies:{Colors.ENDC}")
                print(f"{Colors.RED}{result.stderr}{Colors.ENDC}")
                sys.exit(1)
        else:
            print(f"{Colors.GREEN}Python dependencies installed successfully!{Colors.ENDC}")
    except Exception as e:
        print(f"{Colors.RED}Error installing Python dependencies: {e}{Colors.ENDC}")
        sys.exit(1)
    
    # Check if node_modules exists in frontend directory
    if not os.path.exists(os.path.join('frontend', 'node_modules')):
        print(f"{Colors.YELLOW}Installing Node.js dependencies...{Colors.ENDC}")
        try:
            npm_cmd = 'npm.cmd' if IS_WINDOWS else 'npm'
            subprocess.run(
                [npm_cmd, 'install'],
                cwd='frontend',
                check=True
            )
            print(f"{Colors.GREEN}Node.js dependencies installed successfully!{Colors.ENDC}")
        except subprocess.SubprocessError as e:
            print(f"{Colors.RED}Error installing Node.js dependencies: {e}{Colors.ENDC}")
            sys.exit(1)

def start_backend():
    """Start the Flask backend server"""
    print(f"{Colors.BLUE}Starting backend server on port {BACKEND_PORT}...{Colors.ENDC}")
    
    # Set environment variables
    env = os.environ.copy()
    env['FLASK_APP'] = 'app.py'
    env['FLASK_DEBUG'] = '1'
    
    # Start Flask server
    if IS_WINDOWS:
        backend_process = subprocess.Popen(
            [sys.executable, 'app.py'],
            env=env
        )
    else:
        backend_process = subprocess.Popen(
            [sys.executable, 'app.py'],
            env=env
        )
    
    # Wait for backend to start
    print(f"{Colors.YELLOW}Waiting for backend to start...{Colors.ENDC}")
    time.sleep(2)
    
    return backend_process

def start_frontend():
    """Start the React frontend server"""
    print(f"{Colors.BLUE}Starting React frontend server on port {FRONTEND_PORT}...{Colors.ENDC}")
    print(f"{Colors.YELLOW}Note: This will serve the React application, not the simple HTML interface{Colors.ENDC}")

    # Set environment variables
    env = os.environ.copy()
    env['PORT'] = str(FRONTEND_PORT)
    env['REACT_APP_API_URL'] = f'http://localhost:{BACKEND_PORT}'
    env['BROWSER'] = 'none'  # Prevent React from auto-opening browser

    # Start React development server
    npm_cmd = 'npm.cmd' if IS_WINDOWS else 'npm'
    print(f"{Colors.BLUE}Running: {npm_cmd} start in frontend directory{Colors.ENDC}")
    frontend_process = subprocess.Popen(
        [npm_cmd, 'start'],
        cwd='frontend',
        env=env,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True
    )

    # Wait for frontend to start
    print(f"{Colors.YELLOW}Waiting for React frontend to compile and start...{Colors.ENDC}")
    print(f"{Colors.YELLOW}This may take 1-2 minutes for the first compilation...{Colors.ENDC}")
    time.sleep(10)  # Give React more time to start

    return frontend_process

def open_application():
    """Open the React application in the default web browser"""
    if OPEN_BROWSER:
        print(f"{Colors.BLUE}Opening React application in browser...{Colors.ENDC}")
        print(f"{Colors.YELLOW}URL: http://localhost:{FRONTEND_PORT}{Colors.ENDC}")
        print(f"{Colors.YELLOW}If the page shows 'Loading...' or is blank, the React app is still compiling{Colors.ENDC}")
        webbrowser.open(f'http://localhost:{FRONTEND_PORT}')

def handle_shutdown(backend_process, frontend_process):
    """Handle graceful shutdown of servers"""
    def signal_handler(sig, frame):
        print(f"\n{Colors.YELLOW}Shutting down servers...{Colors.ENDC}")
        
        # Terminate processes
        if frontend_process:
            frontend_process.terminate()
        if backend_process:
            backend_process.terminate()
        
        print(f"{Colors.GREEN}Servers shut down successfully!{Colors.ENDC}")
        sys.exit(0)
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def main():
    """Main function to start the application"""
    print_header()
    check_requirements()
    install_dependencies()
    
    # Start servers
    backend_process = start_backend()
    frontend_process = start_frontend()
    
    # Set up shutdown handler
    handle_shutdown(backend_process, frontend_process)
    
    # Open application in browser
    open_application()
    
    print(f"\n{Colors.GREEN}{Colors.BOLD}React Application is running!{Colors.ENDC}")
    print(f"{Colors.GREEN}Backend API: http://localhost:{BACKEND_PORT}{Colors.ENDC}")
    print(f"{Colors.GREEN}React Frontend: http://localhost:{FRONTEND_PORT}{Colors.ENDC}")
    print(f"\n{Colors.BLUE}Note: This serves the full React application with advanced features{Colors.ENDC}")
    print(f"{Colors.BLUE}For the simple HTML interface, use: python run_app.py{Colors.ENDC}")
    print(f"\n{Colors.YELLOW}Press Ctrl+C to stop the servers{Colors.ENDC}")
    
    # Keep the script running
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}Shutting down servers...{Colors.ENDC}")
        frontend_process.terminate()
        backend_process.terminate()
        print(f"{Colors.GREEN}Servers shut down successfully!{Colors.ENDC}")

if __name__ == "__main__":
    main()
