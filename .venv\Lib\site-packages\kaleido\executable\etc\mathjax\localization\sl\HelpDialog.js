/*************************************************************
 *
 *  MathJax/localization/sl/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("sl","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "Pomo\u010D za MathJax",
          MathJax: "*MathJax* je javaskriptna knji\u017Enica, ki ustvarjal<PERSON>m spletnih strani omogo\u010Da vklju\u010Ditev matematike. Kot bralec ne potrebujete ni\u010Desar, da bi se to zgodilo.",
          Browsers: "*Brskalniki*: MathJax deluje v vseh sodobnih brskalnikih, vklju\u010Dno z IE6+, Firefoxom 3+, Chromeom 0.2+, Safarijem 2+, Opero 9.6+, in v ve\u010Dini mobilnih brskalnikov.",
          Menu: "*Meni ena\u010Db*: MathJax k ena\u010Dbam doda kontekstni meni. Za dostop do menija matematiko desno kliknite ali jo kliknite ob dr\u017Eanju tipke CTRL.",
          ShowMath: "*Prika\u017Ei matematiko kot* vam omogo\u010Da prikaz izvornega ozna\u010Devanja formule za kopiranje in lepljenje (kot MathML ali v izvorni obliki).",
          Settings: "*Nastavitve* vam dajo nadzor nad mo\u017Enostmi MathJaxa, kot so velikost matematike in mehanizem za prikaz ena\u010Db.",
          Language: "*Jezik* vam omogo\u010Da izbrati jezik, ki naj ga MathJax uporablja za svoje menije in opozorilna sporo\u010Dila.",
          Zoom: "*Math Zoom*: \u010De imate te\u017Eave pri branju ena\u010Dbe, jo lahko MathJax pove\u010Da, da jo boste bolje videli.",
          Accessibilty: "*Dostopnost*: MathJax samodejno deluje z bralniki zaslona in omogo\u010Da dostop do matematike tudi slepim in slabovidnim.",
          Fonts: "*Pisave*: MathJax bo uporabljal nekatere matemati\u010Dne pisave, \u010De so name\u0161\u010Dene na ra\u010Dunalniku, sicer pa pisave s spleta. \u010Ceprav niso nujne, bodo lokalno name\u0161\u010Dene pisave pospe\u0161ile prikaz. Predlagamo namestitev [pisav STIX](%1)."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/sl/HelpDialog.js");
