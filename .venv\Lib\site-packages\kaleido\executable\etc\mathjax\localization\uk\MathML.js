/*************************************************************
 *
 *  MathJax/localization/uk/MathML.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("uk","MathML",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          BadMglyph: "\u041D\u0435\u043F\u0440\u0430\u0432\u0438\u043B\u044C\u043D\u0438\u0439 mglyph: %1",
          BadMglyphFont: "\u041D\u0435\u043F\u0440\u0430\u0432\u0438\u043B\u044C\u043D\u0438\u0439 \u0448\u0440\u0438\u0444\u0442:%1",
          MathPlayer: "MathJax \u0431\u0443\u0432 \u043D\u0435 \u0443 \u0437\u043C\u043E\u0437\u0456 \u043D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u0442\u0438 MathPlayer.\n\n\u042F\u043A\u0449\u043E MathPlayer \u043D\u0435 \u0432\u0441\u0442\u0430\u043D\u043E\u0432\u043B\u0435\u043D\u043E\u200B\u200B, \u043D\u0435\u043E\u0431\u0445\u0456\u0434\u043D\u043E \u0432\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0438 \u0439\u043E\u0433\u043E. \u0412 \u0456\u043D\u0448\u043E\u043C\u0443 \u0432\u0438\u043F\u0430\u0434\u043A\u0443, \u0432\u0430\u0448\u0456 \u043D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0431\u0435\u0437\u043F\u0435\u043A\u0438 \u043C\u043E\u0436\u0443\u0442\u044C \u0431\u043B\u043E\u043A\u0443\u0432\u0430\u0442\u0438 \u0443\u043F\u0440\u0430\u0432\u043B\u0456\u043D\u043D\u044F ActiveX \u043F\u0440\u0438 \u0437\u0430\u043F\u0443\u0441\u043A\u0443. \u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0439\u0442\u0435 \u043F\u0443\u043D\u043A\u0442 \u0412\u043B\u0430\u0441\u0442\u0438\u0432\u043E\u0441\u0442\u0456 \u0456\u043D\u0442\u0435\u0440\u043D\u0435\u0442\u0443 \u0443 \u043C\u0435\u043D\u044E \u0406\u043D\u0441\u0442\u0440\u0443\u043C\u0435\u043D\u0442\u0438 \u0456 \u0432\u0438\u0431\u0435\u0440\u0456\u0442\u044C \u0432\u043A\u043B\u0430\u0434\u043A\u0443 \u0411\u0435\u0437\u043F\u0435\u043A\u0430, \u0430 \u043F\u043E\u0442\u0456\u043C \u043D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C \u043A\u043D\u043E\u043F\u043A\u0443 \u0412\u043B\u0430\u0441\u043D\u0438\u0439 \u0440\u0456\u0432\u0435\u043D\u044C. \u041F\u0435\u0440\u0435\u043A\u043E\u043D\u0430\u0439\u0442\u0435\u0441\u044F, \u0449\u043E \u043D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0434\u043B\u044F \"\u0417\u0430\u043F\u0443\u0441\u043A \u0435\u043B\u0435\u043C\u0435\u043D\u0442\u0456\u0432 ActiveX\" \u0456 \"\u0414\u0432\u0456\u0439\u043A\u043E\u0432\u0456 \u043A\u043E\u0434\u0438 \u0456 \u0441\u0446\u0435\u043D\u0430\u0440\u0456\u0457 \u043F\u043E\u0432\u0435\u0434\u0456\u043D\u043A\u0438\"\n\u0432\u043A\u043B\u044E\u0447\u0435\u043D\u0456.\n\n\u0412 \u0434\u0430\u043D\u0438\u0439 \u0447\u0430\u0441 \u0432\u0438 \u043F\u043E\u0431\u0430\u0447\u0438\u0442\u0435 \u043F\u043E\u0432\u0456\u0434\u043E\u043C\u043B\u0435\u043D\u043D\u044F \u043F\u0440\u043E \u043F\u043E\u043C\u0438\u043B\u043A\u0443, \u0430 \u043D\u0435\n\u043D\u0430\u0431\u0440\u0430\u043D\u0443 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u043A\u0443.",
          CantCreateXMLParser: "MathJax \u043D\u0435 \u043C\u043E\u0436\u0435 \u0441\u0442\u0432\u043E\u0440\u0438\u0442\u0438 \u0441\u0438\u043D\u0442\u0430\u043A\u0441\u0438\u0447\u043D\u0438\u0439 \u0430\u043D\u0430\u043B\u0456\u0437\u0430\u0442\u043E\u0440 XML \u0434\u043B\u044F MathML. \u041F\u0435\u0440\u0435\u043A\u043E\u043D\u0430\u0439\u0442\u0435\u0441\u044F, \u0449\u043E \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440 \u201E\u0421\u0446\u0435\u043D\u0430\u0440\u0456\u0457 ActiveX \u043F\u043E\u0437\u043D\u0430\u0447\u0435\u043D\u0456 \u044F\u043A \u0431\u0435\u0437\u043F\u0435\u0447\u043D\u0456 \u0434\u043B\u044F \u0432\u0438\u043A\u043E\u043D\u0430\u043D\u043D\u044F \u0441\u0446\u0435\u043D\u0430\u0440\u0456\u0457\u0432 \u0431\u0435\u0437\u043F\u0435\u043A\u0438\u201C\n \u0443\u0432\u0456\u043C\u043A\u043D\u0435\u043D\u0438\u0439 (\u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0439\u0442\u0435 \u043F\u0443\u043D\u043A\u0442 \u0412\u043B\u0430\u0441\u0442\u0438\u0432\u043E\u0441\u0442\u0456 \u0456\u043D\u0442\u0435\u0440\u043D\u0435\u0442\u0443 \u0432 \u043C\u0435\u043D\u044E \u0406\u043D\u0441\u0442\u0440\u0443\u043C\u0435\u043D\u0442\u0438 \u0456 \u0432\u0438\u0431\u0435\u0440\u0456\u0442\u044C \u043F\u0430\u043D\u0435\u043B\u044C \u0411\u0435\u0437\u043F\u0435\u043A\u0430, \u043F\u043E\u0442\u0456\u043C \u043D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C \u043A\u043D\u043E\u043F\u043A\u0443 \u0412\u043B\u0430\u0441\u043D\u0438\u0439 \u0440\u0456\u0432\u0435\u043D\u044C, \u0449\u043E\u0431 \u0432\u0456\u0434\u043C\u0456\u0442\u0438\u0442\u0438 \u0446\u0435).\n\nMathML \u0440\u0456\u0432\u043D\u044F\u043D\u043D\u044F \u043D\u0435 \u0437\u043C\u043E\u0436\u0443\u0442\u044C \u0431\u0443\u0442\u0438 \u043E\u0431\u0440\u043E\u0431\u043B\u0435\u043D\u0456 MathJax.",
          UnknownNodeType: "\u041D\u0435\u0432\u0456\u0434\u043E\u043C\u0438\u0439 \u0442\u0438\u043F \u0432\u0443\u0437\u043B\u0430: %1",
          UnexpectedTextNode: "\u041D\u0435\u043F\u0435\u0440\u0435\u0434\u0431\u0430\u0447\u0435\u043D\u0438\u0439 \u0442\u0435\u043A\u0441\u0442\u043E\u0432\u0438\u0439 \u0432\u0443\u0437\u043E\u043B: %1",
          ErrorParsingMathML: "\u041F\u043E\u043C\u0438\u043B\u043A\u0430 \u0430\u043D\u0430\u043B\u0456\u0437\u0443 MathML",
          ParsingError: "\u041F\u043E\u043C\u0438\u043B\u043A\u0430 \u0430\u043D\u0430\u043B\u0456\u0437\u0443 MathML: %1",
          MathMLSingleElement: "MathML \u043F\u043E\u0432\u0438\u043D\u0435\u043D \u0431\u0443\u0442\u0438 \u0441\u0444\u043E\u0440\u043C\u043E\u0432\u0430\u043D\u0438\u0439 \u043E\u0434\u043D\u0438\u043C \u0435\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u043C",
          MathMLRootElement: "MathML \u043F\u043E\u0432\u0438\u043D\u0435\u043D \u0431\u0443\u0442\u0438 \u0441\u0444\u043E\u0440\u043C\u043E\u0432\u0430\u043D\u0438\u0439 \u003Cmath\u003E \u0435\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u043C, \u0430 \u043D\u0435 %1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/uk/MathML.js");
