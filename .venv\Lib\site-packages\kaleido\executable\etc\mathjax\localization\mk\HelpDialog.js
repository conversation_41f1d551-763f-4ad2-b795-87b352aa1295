/*************************************************************
 *
 *  MathJax/localization/mk/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("mk","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "\u041F\u043E\u043C\u043E\u0448 \u0441\u043E MathJax",
          MathJax: "*MathJax* \u0435 JavaScript-\u0431\u0438\u0431\u043B\u0438\u043E\u0442\u043A\u0430 \u0448\u0442\u043E \u0438\u043C \u043E\u0432\u043E\u0437\u043C\u043E\u0436\u0443\u0432\u0430 \u043D\u0430 \u0430\u0432\u0442\u043E\u0440\u0438\u0442\u0435 \u0434\u0430 \u0441\u0442\u0430\u0432\u0430\u0430\u0442 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043A\u0438 \u0441\u043E\u0434\u0440\u0436\u0438\u043D\u0438 \u043D\u0430 \u043D\u0438\u0432\u043D\u0438\u0442\u0435 \u0441\u0435\u043C\u0440\u0435\u0436\u043D\u0438 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0438. \u041A\u0430\u043A\u043E \u0447\u0438\u0442\u0430\u0442\u0435\u043B \u043D\u0435 \u0442\u0440\u0435\u0431\u0430 \u0434\u0430 \u043F\u0440\u0430\u0432\u0438\u0442\u0435 \u043D\u0438\u0448\u0442\u043E, \u0431\u0438\u0434\u0435\u0458\u045C\u0438 \u0442\u0438\u0435 \u0441\u0430\u043C\u0438\u0442\u0435 \u045C\u0435 \u0441\u0435 \u043F\u0440\u0438\u043A\u0430\u0436\u0443\u0432\u0430\u0430\u0442.",
          Browsers: "*\u041F\u0440\u0435\u043B\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u0438*: MathJax \u0440\u0430\u0431\u043E\u0442\u0438 \u043D\u0430 \u0441\u0438\u0442\u0435 \u0441\u043E\u0432\u0440\u0435\u043C\u0435\u043D\u0438 \u043F\u0440\u0435\u043B\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u0438 \u043A\u0430\u043A\u043E IE6+, Firefox 3+, Chrome 0.2+, Safari 2+, Opera 9.6+ \u0438 \u043D\u0430\u0458\u0432\u0435\u045C\u0435\u0442\u043E \u043C\u043E\u0431\u0438\u043B\u043D\u0438 \u043F\u0440\u0435\u043B\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u0438.",
          Menu: "*\u041C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043A\u043E \u043C\u0435\u043D\u0438*: MathJax \u0441\u0442\u0430\u0432\u0430 \u043A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u043D\u043E \u043C\u0435\u043D\u0438 \u0432\u043E \u0440\u0430\u0432\u0435\u043D\u043A\u0438\u0442\u0435. \u0414\u043E \u043D\u0435\u0433\u043E \u0441\u0435 \u0434\u043E\u0430\u0453\u0430 \u0441\u043E \u0434\u0435\u0441\u0435\u043D \u0441\u0442\u0438\u0441\u043E\u043A \u0441\u043E \u0433\u043B\u0443\u0448\u0435\u0446\u043E\u0442 (\u0438\u043B\u0438 Ctrl-\u0441\u0442\u0438\u0441\u043E\u043A) \u043D\u0430 \u0431\u0438\u043B\u043E \u043A\u043E\u0458\u0430 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043A\u0430 \u0441\u043E\u0434\u0440\u0436\u0438\u043D\u0430.",
          ShowMath: "*\u041F\u041F\u0440\u0438\u043A\u0430\u0436\u0438 \u0458\u0430 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u043A\u0430\u0442\u0430 \u043A\u0430\u043A\u043E* \u0432\u0438 \u043E\u0432\u043E\u0437\u043C\u043E\u0436\u0443\u0432\u0430 \u0434\u0430 \u0433\u043E \u043F\u043E\u0433\u043B\u0435\u0434\u0430\u0442\u0435 \u0438\u0437\u0432\u043E\u0440\u043D\u0438\u043E\u0442 \u043A\u043E\u0434 \u043D\u0430 \u0444\u043E\u0440\u043C\u0443\u043B\u0430\u0442\u0430 \u0438 \u0434\u0430 \u0433\u043E \u043F\u0440\u0435\u043A\u043E\u043F\u0438\u0440\u0430\u0442\u0435 (\u043A\u0430\u043A\u043E MathML \u0438\u043B\u0438 \u0432\u043E \u0438\u0437\u0432\u043E\u0440\u0435\u043D \u0444\u043E\u0440\u043C\u0430\u0442).",
          Settings: "*\u041F\u043E\u0441\u0442\u0430\u0432\u043A\u0438* \u0432\u0438 \u0434\u0430\u0432\u0430\u0430\u0442 \u043A\u043E\u043D\u0442\u0440\u043E\u043B\u0430 \u0432\u0440\u0437 \u043C\u043E\u0436\u043D\u043E\u0441\u0442\u0438\u0442\u0435 \u043D\u0430 MathJax \u043A\u0430\u043A\u043E \u0433\u043E\u043B\u0435\u043C\u0438\u043D\u0430\u0442\u0430 \u043D\u0430 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043A\u0438\u043E\u0442 \u0438\u0441\u043F\u0438\u0441 \u0438 \u043C\u0435\u0445\u0430\u043D\u0438\u0437\u043C\u043E\u0442 \u0437\u0430 \u043F\u0440\u0438\u043A\u0430\u0437 (\u0438\u0441\u043F\u0438\u0441) \u043D\u0430 \u0440\u0430\u0432\u0435\u043D\u043A\u0438\u0442\u0435.",
          Language: "*\u0408\u0430\u0437\u0438\u043A* \u0432\u0438 \u0434\u0430\u0432\u0430 \u0434\u0430 \u0438\u0437\u0431\u0435\u0440\u0435\u0442\u0435 \u0458\u0430\u0437\u0438\u043A \u043D\u0430 \u043C\u0435\u043D\u0438\u0442\u043E \u0438 \u043F\u0440\u0435\u0434\u0443\u043F\u0440\u0435\u0434\u0443\u0432\u0430\u0447\u043A\u0438\u0442\u0435 \u043F\u043E\u0440\u0430\u043A\u0438 \u043D\u0430 MathJax.",
          Zoom: "*\u0417\u0433\u043E\u043B\u0435\u043C\u0443\u0432\u0430\u045A\u0435 \u043D\u0430 \u0438\u0441\u043F\u0438\u0441\u043E\u0442*: \u0410\u043A\u043E \u0438\u0441\u043F\u0438\u0441\u043E\u0442 \u0432\u0438 \u0435 \u0441\u0438\u0442\u0435\u043D \u0437\u0430 \u0447\u0438\u0442\u0430\u045A\u0435, MathJax \u043C\u043E\u0436\u0435 \u0434\u0430 \u0433\u043E \u0437\u0433\u043E\u043B\u0435\u043C\u0438 \u0437\u0430 \u0434\u0430 \u0432\u0438 \u0431\u0438\u0434\u0435 \u043F\u043E\u0447\u0438\u0442\u043B\u0438\u0432.",
          Accessibilty: "*\u041F\u043E\u0442\u0435\u0448\u043A\u043E\u0442\u0438\u0438 \u0441\u043E \u0432\u0438\u0434\u043E\u0442*: MathJax \u0430\u0432\u0442\u043E\u043C\u0430\u0442\u0441\u043A\u0438 \u0440\u0430\u0431\u043E\u0442\u0438 \u0441\u043E \u0435\u043A\u0440\u0430\u043D\u0441\u043A\u0438\u0442\u0435 \u0447\u0438\u0442\u0430\u0447\u0438 \u0437\u0430 \u0441\u043E\u0434\u0440\u0436\u0438\u043D\u0438\u0442\u0435 \u0434\u0430 \u0431\u0438\u0434\u0430\u0442 \u0458\u0430\u0441\u043D\u0438 \u0437\u0430 \u043B\u0438\u0446\u0430\u0442\u0430 \u0441\u043E \u0445\u0435\u043D\u0434\u0438\u043A\u0435\u043F\u0438\u0440\u0430\u043D \u0432\u0438\u0434.",
          Fonts: "*\u0424\u043E\u043D\u0442\u043E\u0432\u0438*: MathJax \u045C\u0435 \u043A\u043E\u0440\u0438\u0441\u0442\u0438 \u0438\u0437\u0432\u0435\u0441\u043D\u0438 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043A\u0438 \u0444\u043E\u043D\u0442\u043E\u0432\u0438, \u0434\u043E\u043A\u043E\u043B\u043A\u0443 \u0441\u0435 \u0432\u043E\u0441\u043F\u043E\u0441\u0442\u0430\u0432\u0435\u043D\u0438 \u043D\u0430 \u0432\u0430\u0448\u0438\u043E\u0442 \u0441\u043C\u0435\u0442\u0430\u0447. \u0412\u043E \u0441\u043F\u0440\u043E\u0442\u0438\u0432\u043D\u043E \u045C\u0435 \u0441\u0435 \u043A\u043E\u0440\u0438\u0441\u0442\u0430\u0442 \u043C\u0440\u0435\u0436\u043D\u0438 \u0444\u043E\u043D\u0442\u043E\u0432\u0438 \u0437\u0430 \u0442\u0430\u0430 \u043D\u0430\u043C\u0435\u043D\u0430. \u0418\u0430\u043A\u043E \u043D\u0435 \u0441\u0435 \u0437\u0430\u0434\u043E\u043B\u0436\u0438\u0442\u0435\u043B\u043D\u0438, \u043C\u0435\u0441\u043D\u043E \u0432\u043E\u0441\u0442\u0430\u043D\u043E\u0432\u0435\u043D\u0438\u0442\u0435 \u0444\u043E\u043D\u0442\u043E\u0432\u0438 \u0433\u043E \u0437\u0430\u0431\u0440\u0437\u0443\u0432\u0430\u0430\u0442 \u0438\u0441\u043F\u0438\u0441\u043E\u0442. \u0412\u0438 \u043F\u0440\u0435\u0434\u043B\u0430\u0433\u0430\u043C\u0435 \u0434\u0430 \u0433\u0438 \u0432\u043E\u0441\u043F\u043E\u0441\u0442\u0430\u0432\u0438\u0442\u0435 [\u0444\u043E\u043D\u0442\u043E\u0432\u0438\u0442\u0435 \u043D\u0430 STIX](%1).",
          CloseDialog: "\u0417\u0430\u0442\u0432\u043E\u0440\u0438 \u0433\u043E \u043F\u043E\u043C\u043E\u0448\u043D\u0438\u043E\u0442 \u0434\u0438\u0458\u0430\u043B\u043E\u0433"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/mk/HelpDialog.js");
