/*************************************************************
 *
 *  MathJax/localization/qqq/qqq.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("qqq",null,{
  menuTitle: "Message Documentation",
  version: "2.7.5",
  isLoaded: true,
  domains: {
    "_": {
        version: "2.7.5",
        isLoaded: true,
        strings: {
          CookieConfig: "This alert message is displayed when the MathJax cookie contains some data with URL or Config properties. These properties may be used to ask MathJax to perform actions during the Configuration phase: either loading a javascript file (URL property) or executing a configuration function (Config property). Note that the character '\\n' is used to specify new lines inside the alert box.",
          MathProcessingError: "This message appears when a Javascript error happens during the processing of a mathematical element.",
          MathError: "This message appears instead of 'Math Processing Error' when the obsolete Accessible configuration is used.",
          LoadFile: "This appears in the MathJax message box when a file is loading. Parameters:\n* %1 - the file name\n{{Identical|Loading}}",
          Loading: "This appears in the MathJax message box when a file is loading and the messageStyle configuration option is set to 'simple'.\n\nIt will be followed by growing sequence of dots to show the progress.\n{{Identical|Loading}}",
          LoadFailed: "This appears in the MathJax message box when a file fails to load. Parameters:\n* %1 - the file name",
          ProcessMath: "This appears in the MathJax message box during the conversion process from an input format (e.g., LaTeX, asciimath) to MathJax's internal format.\n\nParameters:\n* %1 - a percentage",
          Processing: "This appears in the MathJax message box during the conversion process from an input format (e.g., LaTeX, asciimath) to MathJax's internal format when the messageStyle configuration option is set to 'simple'.\n\nIt will be followed by growing sequence of dots to show the progress.\n{{Identical|Processing}}",
          TypesetMath: "This appears in the MathJax message box during the layout process of converting the internal format to the output format.\n\nParameters:\n* %1 - a percentage",
          Typesetting: "This appears in the MathJax message box during the layout process of converting the internal format to the output format when the messageStyle configuration option is set to 'simple'.\n\nIt will be followed by growing sequence of dots to show the progress.",
          MathJaxNotSupported: "This appears in the MathJax message box when MathJax determines the browser does not have adequate features.",
          ErrorTips: "Debugging tips that appear in 'Show Math As' pop-ups if a rendering error occured."
        }
    },
    "FontWarnings": {},
    "HTML-CSS": {},
    "HelpDialog": {},
    "MathML": {},
    "MathMenu": {},
    "TeX": {}
  },
  plural: function (n) {return 1},
  number: function (n) {return n}
});

MathJax.Ajax.loadComplete("[MathJax]/localization/qqq/qqq.js");
