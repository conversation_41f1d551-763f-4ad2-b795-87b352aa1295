/*************************************************************
 *
 *  MathJax/localization/lt/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("lt","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "\u012Ekeliamas tinklo \u0161riftas %1",
          CantLoadWebFont: "Tinklo \u0161rifto %1 \u012Fkel<PERSON> ne<PERSON>",
          FirefoxCantLoadWebFont: "\u201EFirefox\u201C nepavyksta \u012Fkelti tinklo \u0161rift\u0173 i\u0161 nutolusios prieglobos",
          CantFindFontUsing: "Nepavyksta rasti tinkamo \u0161rifto naudojant %1",
          WebFontsNotAvailable: "Naudojami paveiksliniai \u0161riftai, nes n\u0117ra tinklo \u0161rift\u0173."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/lt/HTML-CSS.js");
