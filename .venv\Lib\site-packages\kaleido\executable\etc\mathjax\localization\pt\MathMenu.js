/*************************************************************
 *
 *  MathJax/localization/pt/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("pt","MathMenu",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Show: "Mostrar f\u00F3rmulas como",
          MathMLcode: "C\u00F3digo MathML",
          OriginalMathML: "MathML original",
          TeXCommands: "Comandos TeX",
          AsciiMathInput: "Entrada AsciiMathML",
          Original: "Formato original",
          ErrorMessage: "Mensagem de erro",
          Annotation: "Anota\u00E7\u00E3o",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "MathML do conte\u00FAdo",
          OpenMath: "OpenMath",
          texHints: "Mostrar dicas de TeX em MathML",
          Settings: "Configura\u00E7\u00F5es das f\u00F3rmulas",
          ZoomTrigger: "Ativador do zoom",
          Hover: "Passar o rato",
          Click: "Clique",
          DoubleClick: "Duplo clique",
          NoZoom: "Sem zoom",
          TriggerRequires: "O ativador requer:",
          Option: "Op\u00E7\u00E3o",
          Alt: "Alt",
          Command: "Comando",
          Control: "Control",
          Shift: "Shift",
          ZoomFactor: "Fator de zoom",
          Renderer: "Renderizador matem\u00E1tico",
          MPHandles: "Deixe que o MathPlayer resolva:",
          MenuEvents: "Eventos de menu",
          MouseEvents: "Eventos do rato",
          MenuAndMouse: "Eventos do rato e de menu",
          FontPrefs: "Prefer\u00EAncias de fontes",
          ForHTMLCSS: "Para HTML-CSS:",
          Auto: "Autom\u00E1tico",
          TeXLocal: "TeX (local)",
          TeXWeb: "TeX (web)",
          TeXImage: "TeX (imagem)",
          STIXLocal: "STIX (local)",
          STIXWeb: "STIX (web)",
          AsanaMathWeb: "Asana Math (web)",
          GyrePagellaWeb: "Gyre Pagella (web)",
          GyreTermesWeb: "Gyre Termes (web)",
          LatinModernWeb: "Latin Modern (web)",
          NeoEulerWeb: "Neo Euler (web)",
          ContextMenu: "Menu de contexto",
          Browser: "Navegador",
          Scale: "Redimensionar todas as f\u00F3rmulas ...",
          Discoverable: "Destacar ao passar com o rato",
          Locale: "L\u00EDngua",
          LoadLocale: "Carregar a partir de URL ...",
          About: "Sobre o MathJax",
          Help: "Ajuda do MathJax",
          localTeXfonts: "a usar fontes TeX locais",
          webTeXfonts: "a usar fontes TeX da web",
          imagefonts: "a usar fontes feitas com imagens",
          localSTIXfonts: "a usar fontes STIX",
          webSVGfonts: "a usar fontes SVG da web",
          genericfonts: "a usar fontes unicode gen\u00E9ricas",
          wofforotffonts: "fontes WOFF ou OTF",
          eotffonts: "fontes EOT",
          svgfonts: "fontes SVG",
          WebkitNativeMMLWarning: "N\u00E3o parece haver suporte nativo ao MathML no seu navegador, ent\u00E3o a mudan\u00E7a para MathML pode tornar ileg\u00EDveis as f\u00F3rmulas matem\u00E1ticas da p\u00E1gina.",
          MSIENativeMMLWarning: "O Internet Explorer requer o plugin MathPlayer para processar MathML.",
          OperaNativeMMLWarning: "O suporte ao MathML no Opera \u00E9 limitado, ent\u00E3o a mudan\u00E7a para MathML pode piorar a renderiza\u00E7\u00E3o de algumas express\u00F5es.",
          SafariNativeMMLWarning: "O suporte ao MathML nativo do seu navegador n\u00E3o implementa todos os recursos usados pelo MathJax, ent\u00E3o algumas express\u00F5es podem n\u00E3o ser exibidas adequadamente.",
          FirefoxNativeMMLWarning: "O suporte ao MathML nativo do seu navegador n\u00E3o implementa todos os recursos usados pelo MathJax, ent\u00E3o algumas express\u00F5es podem n\u00E3o ser exibidas adequadamente.",
          MSIESVGWarning: "N\u00E3o h\u00E1 uma implementa\u00E7\u00E3o de SVG nas vers\u00F5es do Internet Explorer anteriores ao IE9 ou quando ele est\u00E1 emulando o IE8 ou as vers\u00F5es anteriores. A mudan\u00E7a para SVG far\u00E1 com que as f\u00F3rmulas n\u00E3o sejam exibidas adequadamente.",
          LoadURL: "Carregar os dados de tradu\u00E7\u00E3o a partir desta URL:",
          BadURL: "A URL deve ser para um um ficheiro de JavaScript que defina os dados de tradu\u00E7\u00E3o do MathJax. Os nomes dos ficheiros de Javascript devem terminar com '.js'",
          BadData: "Falha ao carregar os dados de tradu\u00E7\u00E3o de %1",
          SwitchAnyway: "Mudar para este renderizador mesmo assim?\n\n(Pressione OK para mudar, CANCELAR para continuar com o renderizador atual)",
          ScaleMath: "Redimensionar todas as f\u00F3rmulas matem\u00E1ticas (em rela\u00E7\u00E3o ao texto \u00E0 sua volta) em",
          NonZeroScale: "A escala n\u00E3o deve ser zero",
          PercentScale: "A escala deve ser uma percentagem (por exemplo, 120%%)",
          IE8warning: "Isto desabilitar\u00E1 o menu MathJax e os recursos de zoom, mas voc\u00EA poder\u00E1 usar Alt-Clique em uma express\u00E3o para obter o menu MathJax em vez disso.\n\nDeseja realmente alterar as configura\u00E7\u00F5es do MathPlayer?",
          IE9warning: "O menu de contexto do MathJax ser\u00E1 desabilitado, mas pode usar Alt-Clique numa express\u00E3o para obter o menu MathJax em vez disso.",
          NoOriginalForm: "Sem uma forma original dispon\u00EDvel",
          Close: "Fechar",
          EqSource: "C\u00F3digo de equa\u00E7\u00E3o MathJax",
          CloseAboutDialog: "Fechar caixa sobre MathJax",
          FastPreview: "Pr\u00E9-visualiza\u00E7\u00E3o r\u00E1pida",
          AssistiveMML: "MAthML assistiva",
          InTabOrder: "Incluir na ordem da guia"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/pt/MathMenu.js");
