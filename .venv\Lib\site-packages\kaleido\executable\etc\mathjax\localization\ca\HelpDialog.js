/*************************************************************
 *
 *  MathJax/localization/ca/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ca","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "Ajuda MathJax",
          MathJax: "*MathJax* \u00E9s una llibreria JavaScript que permet als autors de p\u00E0gines incloure expressions matem\u00E0tiques a les seves p\u00E0gines web. Com a lector, no necessiteu res perqu\u00E8 pugueu visualitzar la p\u00E0gina.",
          Browsers: "*Navegadors*: MathJax funciona amb tots els navegadors moderns, incloent IE6+, Firefox 3+, Chrome 0.2+, Safari 2+, Opera 9.6+ i la majoria de navegadors m\u00F2bils.",
          Menu: "*Men\u00FA matem\u00E0tiques*: MathJax afegeix un men\u00FA contextual a les equacions. Feu clic amb el bot\u00F3 dret, o Ctrl-clic, sobre qualsevol f\u00F3rmula per accedir al men\u00FA.",
          ShowMath: "*Mostra f\u00F3rmula com a* us permet visualitzar el codi font de la f\u00F3rmula, per tal que pugueu copiar i enganxar (en format MathML o en el seu format original).",
          Settings: "*Configuraci\u00F3* us permet controlar diverses caracter\u00EDstiques de MathJax, com la mida de les f\u00F3rmules, i el mecanisme emprat per visualitzar les equacions.",
          Language: "*Idioma* us permet seleccionar l'idioma emprat per MathJax en els seus men\u00FAs i missatges d'advert\u00E8ncia.",
          Zoom: "*Zoom*: Si teniu dificultats a l'hora de llegir una equaci\u00F3, MathJax pot ampliar-la per ajudar-vos a veure-la millor.",
          Accessibilty: "*Accessibilitat*: MathJax funciona autom\u00E0ticament amb lectors de pantalla, per a fer les expressions matem\u00E0tiques accessibles a aquells que tenen dificultats de visi\u00F3.",
          Fonts: "*Fonts*: MathJax usa algunes fonts matem\u00E0tiques si estan instal\u00B7lades al vostre ordinador; si no les hi troba, usar\u00E0 fonts basades en la web. Encara que no \u00E9s necessari, si instal\u00B7leu les fonts localment, aix\u00F2i ajudar\u00E0 a qu\u00E8 la composici\u00F3 de la p\u00E0gina sigui m\u00E9s r\u00E0pida. Us suggerim que instal\u00B7leu les [fonts STIX](%1).",
          CloseDialog: "Tanca el di\u00E0leg d'ajuda"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ca/HelpDialog.js");
