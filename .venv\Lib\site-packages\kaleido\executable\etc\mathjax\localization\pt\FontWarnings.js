/*************************************************************
 *
 *  MathJax/localization/pt/FontWarnings.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("pt","FontWarnings",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          webFont: "O MathJax est\u00E1 a utilizar fontes baseadas em web para exibir as f\u00F3rmulas matem\u00E1ticas desta p\u00E1gina. A sua descarga leva algum tempo, ent\u00E3o a p\u00E1gina seria renderizada mais rapidamente se instalasse as fontes para matem\u00E1tica diretamente no diret\u00F3rio de fontes do seu sistema.",
          imageFonts: "O MathJax est\u00E1 a utilizar fontes feitas com imagens em vez de fontes locais ou baseadas em web. Isso torna a renderiza\u00E7\u00E3o mais lenta do que o de costume, e as f\u00F3rmulas matem\u00E1ticas poder\u00E3o n\u00E3o ser impressas com a maior resolu\u00E7\u00E3o dispon\u00EDvel na sua impressora.",
          noFonts: "O MathJax n\u00E3o foi capaz de localizar uma fonte para utilizar ao renderizar as f\u00F3rmulas matem\u00E1ticas, e n\u00E3o est\u00E3o dispon\u00EDveis fontes feitas com imagens, ent\u00E3o ser\u00E3o utilizados caracteres Unicode gen\u00E9ricos com a esperan\u00E7a de que o seu navegador seja capaz de exibi-los. Alguns caracteres podem n\u00E3o aparecer como deveriam, ou simplesmente desaparecer.",
          webFonts: "A maioria dos navegadores modernos permite que as fontes sejam descarregadas a partir da web. Atualizar para uma vers\u00E3o mais recente do seu navegador (ou mudar de navegador) poderia melhorar a qualidade das f\u00F3rmulas matem\u00E1ticas desta p\u00E1gina.",
          fonts: "O MathJax pode usar tanto [fontes STIX](%1) ou as [fontes MathJax TeX](%2). Descarregue e instale estas fontes para melhorar a sua experi\u00EAncia com o MathJax.",
          STIXPage: "Esta p\u00E1gina foi projetada para utilizar [fontes STIX](%1). Descarregue e instale estas fontes para melhorar a sua experi\u00EAncia com o MathJax.",
          TeXPage: "Esta p\u00E1gina foi projetada para utilizar [fontes MathJax TeX](%1). Descarregue e instale estas fontes para melhorar a sua experi\u00EAncia com o MathJax."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/pt/FontWarnings.js");
