/*************************************************************
 *
 *  MathJax/localization/nl/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("nl","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "Weblettertype %1 aan het laden",
          CantLoadWebFont: "Het weblettertype %1 kan niet geladen worden.",
          FirefoxCantLoadWebFont: "Firefox kan geen web-lettertypes laden van een externe host",
          CantFindFontUsing: "Kan met gebruik van %1 geen geldig lettertype vinden",
          WebFontsNotAvailable: "Weblettertypes zijn niet beschikbaar. In plaats daarvan worden afbeeldingslettertypes gebruikt"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/nl/HTML-CSS.js");
