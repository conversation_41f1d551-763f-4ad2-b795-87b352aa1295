import React, { useState, useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import {
  AppBar,
  Toolbar,
  Typography,
  Container,
  Stepper,
  Step,
  StepLabel,
  Box,
  Alert,
  LinearProgress,
  IconButton,
  Tooltip,
  Chip
} from '@mui/material';
import { Toaster } from 'react-hot-toast';
import {
  Refresh,
  DarkMode,
  LightMode,
  Info
} from '@mui/icons-material';

// Import Evos logo
import evosLogo from './logo.png';

// Import components
import DataUpload from './components/DataUpload';
import DataValidation from './components/DataValidation';
import DataProcessing from './components/DataProcessing';
import FeatureManagement from './components/FeatureManagement';
import ModelTraining from './components/ModelTraining';
import ModelEvaluation from './components/ModelEvaluation';
import PredictionInterface from './components/PredictionInterface';

// Import services
import { apiService } from './services/apiService';

const App = () => {
  const [darkMode, setDarkMode] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [appStatus, setAppStatus] = useState({
    dataLoaded: false,
    modelsTrained: false,
    bestModel: null,
    dataShape: null
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const theme = createTheme({
    palette: {
      mode: darkMode ? 'dark' : 'light',
      primary: {
        main: '#FF6B35', // Evos orange
      },
      secondary: {
        main: '#1976d2', // Keep blue as secondary
      },
      background: {
        default: darkMode ? '#121212' : '#f5f5f5',
        paper: darkMode ? '#1e1e1e' : '#ffffff',
      },
    },
    typography: {
      fontFamily: '"Blinker", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif',
      h4: {
        fontWeight: 600,
      },
      h6: {
        fontWeight: 500,
      },
    },
    components: {
      MuiPaper: {
        styleOverrides: {
          root: {
            transition: 'all 0.3s ease',
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            textTransform: 'none',
            fontWeight: 600,
            borderRadius: 8,
          },
        },
      },
    },
  });

  const steps = [
    'Upload Data',
    'Validate Data',
    'Process Data',
    'Manage Features',
    'Train Models',
    'Evaluate Models',
    'Make Predictions'
  ];

  // Check application status on mount
  useEffect(() => {
    checkStatus();
  }, []);

  const checkStatus = async () => {
    try {
      setLoading(true);
      const status = await apiService.getStatus();
      setAppStatus({
        dataLoaded: status.data_loaded,
        modelsTrained: status.models_trained,
        bestModel: status.best_model,
        dataShape: status.data_shape
      });
      
      // Set appropriate step based on status
      if (status.models_trained) {
        setActiveStep(6); // Predictions
      } else if (status.data_loaded) {
        setActiveStep(1); // Validation
      } else {
        setActiveStep(0); // Upload
      }
      setLoading(false);
    } catch (error) {
      console.error('Error checking status:', error);
      setLoading(false);
    }
  };

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleStepClick = (step) => {
    // Allow navigation to previous steps or current step
    if (step <= activeStep) {
      setActiveStep(step);
    }
  };

  const handleDataUploaded = () => {
    setAppStatus(prev => ({ ...prev, dataLoaded: true }));
    handleNext();
  };

  const handleModelsTrained = (bestModel) => {
    setAppStatus(prev => ({ 
      ...prev, 
      modelsTrained: true, 
      bestModel: bestModel 
    }));
    handleNext();
  };

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <DataUpload 
            onDataUploaded={handleDataUploaded}
            setLoading={setLoading}
            setError={setError}
          />
        );
      case 1:
        return (
          <DataValidation 
            onNext={handleNext}
            onBack={handleBack}
            setLoading={setLoading}
            setError={setError}
          />
        );
      case 2:
        return (
          <DataProcessing 
            onNext={handleNext}
            onBack={handleBack}
            setLoading={setLoading}
            setError={setError}
          />
        );
      case 3:
        return (
          <FeatureManagement 
            onNext={handleNext}
            onBack={handleBack}
            setLoading={setLoading}
            setError={setError}
          />
        );
      case 4:
        return (
          <ModelTraining 
            onModelsTrained={handleModelsTrained}
            onBack={handleBack}
            setLoading={setLoading}
            setError={setError}
          />
        );
      case 5:
        return (
          <ModelEvaluation 
            onNext={handleNext}
            onBack={handleBack}
            bestModel={appStatus.bestModel}
            setLoading={setLoading}
            setError={setError}
          />
        );
      case 6:
        return (
          <PredictionInterface 
            onBack={handleBack}
            bestModel={appStatus.bestModel}
            setLoading={setLoading}
            setError={setError}
          />
        );
      default:
        return <Typography>Unknown step</Typography>;
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <div className="app-container">
        <AppBar position="static" elevation={1} className="evos-header">
          <Toolbar>
            <img src={evosLogo} alt="Evos Logo" className="evos-logo" />
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
                Evos Pump Time Prediction
              </Typography>
              <Typography variant="caption" className="evos-tagline">
                Let's Evolve Together
              </Typography>
            </Box>
            
            {appStatus.dataLoaded && (
              <Tooltip title="Data Status">
                <Chip 
                  label={appStatus.dataShape ? 
                    `${appStatus.dataShape[0]} rows × ${appStatus.dataShape[1]} cols` : 
                    'Data Loaded'
                  }
                  color="primary"
                  variant="outlined"
                  size="small"
                  sx={{ mr: 2 }}
                />
              </Tooltip>
            )}
            
            {appStatus.bestModel && (
              <Tooltip title="Best Model">
                <Chip 
                  label={`Model: ${appStatus.bestModel}`}
                  color="success"
                  variant="outlined"
                  size="small"
                  sx={{ mr: 2 }}
                />
              </Tooltip>
            )}
            
            <Tooltip title="Refresh Status">
              <IconButton 
                color="inherit" 
                onClick={checkStatus}
                disabled={loading}
              >
                <Refresh />
              </IconButton>
            </Tooltip>
            
            <Tooltip title={darkMode ? "Light Mode" : "Dark Mode"}>
              <IconButton 
                color="inherit" 
                onClick={toggleDarkMode}
              >
                {darkMode ? <LightMode /> : <DarkMode />}
              </IconButton>
            </Tooltip>
          </Toolbar>
        </AppBar>

        {loading && <LinearProgress />}

        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
          {error && (
            <Alert 
              severity="error" 
              onClose={() => setError(null)}
              sx={{ mb: 2 }}
            >
              {error}
            </Alert>
          )}

          <Box sx={{ width: '100%', mb: 4 }}>
            <Stepper activeStep={activeStep} alternativeLabel>
              {steps.map((label, index) => (
                <Step 
                  key={label} 
                  completed={index < activeStep}
                  sx={{ cursor: index <= activeStep ? 'pointer' : 'default' }}
                  onClick={() => handleStepClick(index)}
                >
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
          </Box>

          <Box sx={{ mt: 2 }}>
            {renderStepContent(activeStep)}
          </Box>
        </Container>

        <Toaster 
          position="bottom-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: darkMode ? '#333' : '#363636',
              color: '#fff',
            },
          }}
        />
      </div>
    </ThemeProvider>
  );
};

export default App;
