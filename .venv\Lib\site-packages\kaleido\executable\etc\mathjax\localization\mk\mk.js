/*************************************************************
 *
 *  MathJax/localization/mk/mk.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("mk",null,{
  menuTitle: "\u043C\u0430\u043A\u0435\u0434\u043E\u043D\u0441\u043A\u0438",
  version: "2.7.5",
  isLoaded: true,
  domains: {
    "_": {
        version: "2.7.5",
        isLoaded: true,
        strings: {
          CookieConfig: "MathJax \u043F\u0440\u043E\u043D\u0430\u0458\u0434\u0435 \u043A\u043E\u043B\u0430\u0447\u0435 \u0441\u043E \u043A\u043E\u0440\u0438\u0441\u043D\u0438\u0447\u043A\u0438 \u043F\u043E\u0441\u0442\u0430\u0432\u043A\u0438 \u043A\u043E\u0435 \u0441\u043E\u0434\u0440\u0436\u0438 \u043A\u043E\u0434 \u0448\u0442\u043E \u0442\u0440\u0435\u0431\u0430 \u0434\u0430 \u0441\u0435 \u043F\u0443\u0448\u0442\u0438. \u0421\u0430\u043A\u0430\u0442\u0435 \u0434\u0430 \u0433\u043E \u043F\u0443\u0448\u0442\u0438\u0442\u0435?\n\n(\u0421\u0442\u0438\u0441\u043D\u0435\u0442\u0435 \u043D\u0430 \u201E\u041E\u0442\u043A\u0430\u0436\u0438\u201C \u0434\u043E\u043A\u043E\u043B\u043A\u0443 \u043D\u0435 \u0441\u0442\u0435 \u0433\u043E \u043F\u043E\u0441\u0442\u0430\u0432\u0438\u043B\u0435 \u043A\u043E\u043B\u0430\u0447\u0435\u0442\u043E \u0432\u0438\u0435 \u0441\u0430\u043C\u0438\u0442\u0435.)",
          MathProcessingError: "\u0413\u0440\u0435\u0448\u043A\u0430 \u0432\u043E \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043A\u0430\u0442\u0430 \u043E\u0431\u0440\u0430\u0431\u043E\u0442\u043A\u0430",
          MathError: "\u041C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043A\u0430 \u0433\u0440\u0435\u0448\u043A\u0430",
          LoadFile: "\u0413\u043E \u0432\u0447\u0438\u0442\u0443\u0432\u0430\u043C %1",
          Loading: "\u0412\u0447\u0438\u0442\u0443\u0432\u0430\u043C",
          LoadFailed: "\u041F\u043E\u0434\u0430\u0442\u043E\u0442\u0435\u043A\u0430\u0442\u0430 \u043D\u0435 \u0441\u0435 \u0432\u0447\u0438\u0442\u0430: %1",
          ProcessMath: "\u041C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043A\u0430 \u043E\u0431\u0440\u0430\u0431\u043E\u0442\u043A\u0430: %1%%",
          Processing: "\u041E\u0431\u0440\u0430\u0431\u043E\u0442\u0443\u0432\u0430\u043C",
          TypesetMath: "\u0421\u043B\u043E\u0436\u0443\u0432\u0430\u045A\u0435 \u043D\u0430 \u0438\u0441\u043F\u0438\u0441\u043E\u0442: %1%%",
          Typesetting: "\u0421\u043B\u043E\u0436\u0443\u0432\u0430\u043C \u0438\u0441\u043F\u0438\u0441",
          MathJaxNotSupported: "\u0412\u0430\u0448\u0438\u043E\u0442 \u043F\u0440\u0435\u0431\u0430\u0440\u0443\u0432\u0430\u0447 \u043D\u0435 \u0433\u043E \u043F\u043E\u0434\u0434\u0440\u0436\u0443\u0432\u0430 MathJax",
          ErrorTips: "\u0421\u043E\u0432\u0435\u0442\u0438 \u0437\u0430 \u0438\u0441\u043F\u0440\u0430\u0432\u0430\u045A\u0435 \u0433\u0440\u0435\u0448\u043A\u0438: \u043A\u043E\u0440\u0438\u0441\u0442\u0435\u0442\u0435 %%1, \u043F\u0440\u0435\u0433\u043B\u0435\u0434\u0430\u0458\u0442\u0435 \u0433\u043E %%2 \u0432\u043E \u043F\u0440\u0435\u043B\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u043A\u0430\u0442\u0430 \u043A\u043E\u043D\u0437\u043E\u043B\u0430"
        }
    },
    "FontWarnings": {},
    "HTML-CSS": {},
    "HelpDialog": {},
    "MathML": {},
    "MathMenu": {},
    "TeX": {}
  },
  plural: function (n) {
      if (n % 10 === 1 && n !== 11) return 1; // one
      return 2; // other
    },
  number: function (n) {
      return n;
    }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/mk/mk.js");
