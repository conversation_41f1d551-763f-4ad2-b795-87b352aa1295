/*************************************************************
 *
 *  MathJax/localization/da/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("da","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "MathJax hj\u00E6lp",
          MathJax: "*MathJax* er et JavaScript-bibliotek, der giver mulighed for forfattere af sider kan inkludere matematik i deres websider. Som l\u00E6ser beh\u00F8ver du ikke at g\u00F8re noget for at f\u00E5 det til at virke.",
          Browsers: "*Browsere*: MathJax virker p\u00E5 alle moderne browsere inklusiv IE6+, Firefox 3+, Chrome 0.2+, Safari 2+, Opera 9.6+ og de fleste mobile browsere.",
          Menu: "*Matematikmenu*: MathJax tilf\u00F8jer en genvejsmenu til ligninger. H\u00F8jreklik eller Ctrl-klik p\u00E5 matematikken for at f\u00E5 adgang til menuen.",
          ShowMath: "*Vis matematik som* giver dig mulighed for at se formelens kilde markup s\u00E5 du kan kopierer og inds\u00E6tte andre steder (som MathML eller i dens oprindelige format).",
          Settings: "*Indstillinger* giver dig kontrol over funktioner i MathJax, s\u00E5som st\u00F8rrelsen af matematikken, og den mekanisme, der bruges til at vise ligninger.",
          Language: "*Sprog* lader dig v\u00E6lge det sprog MathJax bruger i sine menuer og advarselsmeddelelser.",
          Zoom: "*Matematik zoom*: Hvis du har sv\u00E6rt ved at l\u00E6se en ligning, kan MathJax forst\u00F8rre den for at hj\u00E6lpe dig med at se den bedre.",
          Accessibilty: "*Tilg\u00E6ngelighed*: MathJax vil automatisk arbejde med sk\u00E6rml\u00E6sere for at g\u00F8re matematik tilg\u00E6ngeligt for synsh\u00E6mmede.",
          Fonts: "*Skrifttyper*: MathJax vil bruge visse matematiske skrifttyper, hvis de er installeret p\u00E5 computeren. ellers vil det bruge web-baserede skrifttyper. Selvom det ikke er p\u00E5kr\u00E6vet, vil lokalt installerede skrifttyper fremskynde ops\u00E6tningen. Vi foresl\u00E5r at installere [STIX fonts](%1).",
          CloseDialog: "Luk hj\u00E6lpedialogen"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/da/HelpDialog.js");
