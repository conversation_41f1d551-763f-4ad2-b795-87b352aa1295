/*************************************************************
 *
 *  MathJax/localization/it/it.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("it",null,{
  menuTitle: "italiano",
  version: "2.7.5",
  isLoaded: true,
  domains: {
    "_": {
        version: "2.7.5",
        isLoaded: true,
        strings: {
          CookieConfig: "MathJax ha trovato un cookie di configurazione utente che include del codice eseguibile. Vuoi eseguirlo?\n\n(Premi Annulla a meno che non l'abbia effettivamente impostato tu.)",
          MathProcessingError: "Errore elaborazione della formula",
          MathError: "Errore nella formula",
          LoadFile: "Caricamento %1",
          Loading: "Caricamento",
          LoadFailed: "Caricamento del file fallito: %1",
          ProcessMath: "Elaborazione formula: %1%%",
          Processing: "Elaborazione in corso",
          TypesetMath: "Composizione della formula: %1%%",
          Typesetting: "Composizione",
          MathJaxNotSupported: "Il tuo browser non supporta MathJax",
          ErrorTips: "Suggerimenti per il debug: utilizza %%1, ispeziona %%2 nella console del browser"
        }
    },
    "FontWarnings": {},
    "HTML-CSS": {},
    "HelpDialog": {},
    "MathML": {},
    "MathMenu": {},
    "TeX": {}
  },
  plural: function (n) {
      if (n === 1) {return 1} // one
      return 2; // other
    },
  number: function (n) {
      return n;
    }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/it/it.js");
