/*************************************************************
 *
 *  MathJax/localization/fr/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("fr","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "T\u00E9l\u00E9chargement de la police web %1",
          CantLoadWebFont: "Impossible de t\u00E9l\u00E9charger la police web %1",
          FirefoxCantLoadWebFont: "Firefox ne peut pas charger les polices web depuis un h\u00F4te distant",
          CantFindFontUsing: "Impossible de trouver une police valide en utilisant %1",
          WebFontsNotAvailable: "Polices web non disponibles. Les polices image seront utilis\u00E9es \u00E0 la place"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/fr/HTML-CSS.js");
