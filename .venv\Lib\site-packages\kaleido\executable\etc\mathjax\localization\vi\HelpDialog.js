/*************************************************************
 *
 *  MathJax/localization/vi/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("vi","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "Tr\u1EE3 gi\u00FAp MathJax",
          MathJax: "*MathJax* l\u00E0 m\u1ED9t th\u01B0 vi\u1EC7n JavaScript cho ph\u00E9p c\u00E1c t\u00E1c gi\u1EA3 nh\u00FAng c\u00F4ng th\u1EE9c to\u00E1n h\u1ECDc v\u00E0o trang Web. C\u00E1c \u0111\u1ED9c gi\u1EA3 nh\u01B0 b\u1EA1n kh\u00F4ng c\u1EA7n l\u00E0m th\u00EAm g\u00EC \u0111\u1EC3 hi\u1EC3n th\u1ECB to\u00E1n h\u1ECDc.",
          Browsers: "*Tr\u00ECnh duy\u1EC7t*: MathJax t\u01B0\u01A1ng th\u00EDch v\u1EDBi t\u1EA5t c\u1EA3 c\u00E1c tr\u00ECnh duy\u1EC7t \u0111\u1EDDi m\u1EDBi, bao g\u1ED3m IE 6+, Firefox 3+, Chrome 0.2+, Safari 2+, Opera 9.6+, v\u00E0 ph\u1EA7n nhi\u1EC1u tr\u00ECnh duy\u1EC7t di \u0111\u1ED9ng.",
          Menu: "*Tr\u00ECnh \u0111\u01A1n To\u00E1n h\u1ECDc*: MathJax th\u00EAm m\u1ED9t tr\u00ECnh \u0111\u01A1n ng\u1EEF c\u1EA3nh v\u00E0o c\u00E1c ph\u01B0\u01A1ng tr\u00ECnh. Nh\u1EA5n chu\u1ED9t ph\u1EA3i ho\u1EB7c nh\u1EA5n gi\u1EEF Ctrl v\u00E0 nh\u1EA5n chu\u1ED9t tr\u00E1i \u0111\u1EC3 m\u1EDF tr\u00ECnh \u0111\u01A1n n\u00E0y.",
          ShowMath: "*Xem To\u00E1n D\u01B0\u1EDBi d\u1EA1ng* cho ph\u00E9p xem v\u00E0 ch\u00E9p d\u00E1n m\u00E3 ngu\u1ED3n c\u1EE7a ph\u01B0\u01A1ng tr\u00ECnh (d\u01B0\u1EDBi d\u1EA1ng MathML ho\u1EB7c \u0111\u1ECBnh d\u1EA1ng g\u1ED1c).",
          Settings: "*T\u00F9y ch\u1ECDn To\u00E1n* cho ph\u00E9p \u0111i\u1EC1u khi\u1EC3n c\u00E1c t\u00EDnh n\u0103ng c\u1EE7a MathJax, th\u00ED d\u1EE5 nh\u01B0 k\u00EDch th\u01B0\u1EDBc k\u00FD t\u1EF1 to\u00E1n h\u1ECDc, c\u0169ng nh\u01B0 ph\u01B0\u01A1ng ph\u00E1p hi\u1EC3n th\u1ECB c\u00E1c ph\u01B0\u01A1ng tr\u00ECnh.",
          Language: "*Ng\u00F4n ng\u1EEF* cho ph\u00E9p l\u1EF1a ch\u1ECDn ng\u00F4n ng\u1EEF cho c\u00E1c tr\u00ECnh \u0111\u01A1n v\u00E0 th\u00F4ng \u0111i\u1EC7p trong MathJax.",
          Zoom: "*Ph\u00F3ng to*: N\u1EBFu b\u1EA1n c\u1EA3m th\u1EA5y kh\u00F3 \u0111\u1ECDc m\u1ED9t ph\u01B0\u01A1ng tr\u00ECnh, MathJax c\u00F3 th\u1EC3 ph\u00F3ng to n\u00F3 \u0111\u1EC3 gi\u00FAp b\u1EA1n th\u1EA5y r\u00F5 h\u01A1n.",
          Accessibilty: "*Tr\u1EE3 n\u0103ng*: MathJax t\u1EF1 \u0111\u1ED9ng t\u00E1c \u0111\u1ED9ng v\u1EDBi c\u00E1c tr\u00ECnh \u0111\u1ECDc m\u00E0n h\u00ECnh \u0111\u1EC3 gi\u00FAp ng\u01B0\u1EDDi m\u00F9 l\u00F2a truy c\u1EADp to\u00E1n h\u1ECDc.",
          Fonts: "*Ph\u00F4ng ch\u1EEF*: MathJax s\u1EED d\u1EE5ng m\u1ED9t s\u1ED1 ph\u00F4ng ch\u1EEF to\u00E1n h\u1ECDc n\u1EBFu \u0111\u01B0\u1EE3c c\u00E0i \u0111\u1EB7t tr\u00EAn m\u00E1y c\u1EE7a b\u1EA1n; n\u1EBFu kh\u00F4ng, n\u00F3 t\u1EA3i c\u00E1c ph\u00F4ng ch\u1EEF Web. B\u1EA1n c\u00F3 th\u1EC3 (nh\u01B0ng kh\u00F4ng c\u1EA7n ph\u1EA3i) c\u00E0i \u0111\u1EB7t c\u00E1c ph\u00F4ng ch\u1EEF v\u00E0o m\u00E1y \u0111\u1EC3 t\u0103ng t\u1ED1c \u0111\u1ED9 s\u1EAFp ch\u1EEF. Ch\u00FAng t\u00F4i khuy\u1EBFn kh\u00EDch c\u00E0i \u0111\u1EB7t c\u00E1c [ph\u00F4ng ch\u1EEF STIX](%1).",
          CloseDialog: "\u0110\u00F3ng h\u1ED9p tho\u1EA1i tr\u1EE3 gi\u00FAp"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/vi/HelpDialog.js");
