/*************************************************************
 *
 *  MathJax/localization/sl/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("sl","MathMenu",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Show: "Prika\u017Ei matematiko kot",
          MathMLcode: "Koda MathML",
          OriginalMathML: "Izvorni MathML",
          TeXCommands: "Ukazi TeX",
          AsciiMathInput: "Vnos AsciiMathML",
          Original: "Izvirna oblika",
          ErrorMessage: "Sporo\u010Dilo o napaki",
          Annotation: "Pripis",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "MathML vsebine",
          OpenMath: "OpenMath",
          texHints: "V MathML prika\u017Ei namige TeX",
          Settings: "Nastavitve matematike",
          ZoomTrigger: "Pro\u017Eilec zumiranja",
          Hover: "Lebdenje",
          Click: "Kliknite",
          DoubleClick: "Dvokliknite",
          NoZoom: "Brez pove\u010Dave",
          TriggerRequires: "Pro\u017Eilec potrebuje:",
          Option: "Mo\u017Enost",
          Alt: "Alt",
          Command: "Ukaz",
          Control: "Kontrolnik",
          Shift: "Preklop",
          ZoomFactor: "Faktor zumiranja",
          Renderer: "Matemati\u010Dni izrisovalnik",
          MPHandles: "MathPlayer naj upravlja z:",
          MenuEvents: "Dogodki menija",
          MouseEvents: "Dogodki mi\u0161ke",
          MenuAndMouse: "Dogodki mi\u0161ke in menija",
          FontPrefs: "Nastavitve pisav",
          ForHTMLCSS: "Za HTML-CSS:",
          Auto: "Samodejno",
          TeXLocal: "TeX (lokalno)",
          TeXWeb: "TeX (splet)",
          TeXImage: "TeX (slika)",
          STIXLocal: "STIX (lokalno)",
          STIXWeb: "STIX (splet)",
          AsanaMathWeb: "Asana Math (splet)",
          GyrePagellaWeb: "Gyre Pagella (splet)",
          GyreTermesWeb: "Gyre Termes (splet)",
          LatinModernWeb: "Latin Modern (splet)",
          NeoEulerWeb: "Neo Euler (splet)",
          ContextMenu: "Kontekstni meni",
          Browser: "Brskalnik",
          Scale: "Umeri vso matematiko ...",
          Discoverable: "Ob lebdenju ozna\u010Di",
          Locale: "Jezik",
          LoadLocale: "Nalo\u017Ei iz spletnega naslova ...",
          About: "O MathJax",
          Help: "Pomo\u010D za MathJax",
          localTeXfonts: "z uporabo lokalnih pisav TeX",
          webTeXfonts: "z uporabo spletne pisave TeX",
          imagefonts: "z uporabo slikovnih pisav",
          localSTIXfonts: "z uporabo lokalnih pisav STIX",
          webSVGfonts: "z uporabo spletnih pisav SVG",
          genericfonts: "z uporabo generi\u010Dnih unikodnih pisav",
          wofforotffonts: "pisave woff ali otf",
          eotffonts: "pisave eot",
          svgfonts: "pisave svg",
          WebkitNativeMMLWarning: "Va\u0161 brskalnik ne podpira MathML sam po sebi, zato ob preklopu na MathML matematika morda ne bo berljiva.",
          MSIENativeMMLWarning: "Internet Explorer potrebuje za obdelavo izhoda MathML vti\u010Dnik MathPlayer.",
          OperaNativeMMLWarning: "Podpora Opere za MathML je omejena, zato se bodo ob preklopu na MathML nekateri izrazi morda slabo izrisali.",
          SafariNativeMMLWarning: "MathML va\u0161ega brskalnika ne podpira vseh mo\u017Enosti MathJaxa, zato se nekateri izrazi morda ne bodo pravilno prikazali.",
          FirefoxNativeMMLWarning: "MathML va\u0161ega brskalnika ne podpira vseh mo\u017Enosti MathJaxa, zato se nekateri izrazi morda ne bodo pravilno izrisali.",
          MSIESVGWarning: "V Internet Explorerju pred IE9 in pri emulaciji IE8 ali manj SVG ni podprt. Ob preklopu na SVG se matematika ne bo prikazala pravilno.",
          LoadURL: "Nalo\u017Ei podatke za prevajanje z naslednjega spletnega naslova:",
          BadURL: "Spletni naslov mora biti za javaskriptno datoteko, ki opredeljuje podatke MathJax o prevajanju. Imena javaskriptnih datotek se morajo kon\u010Dati z '.js'",
          BadData: "Nalaganje podatkov za prevajanje iz %1 ni uspelo",
          SwitchAnyway: "Vseeno zamenjam izrisovalnik?",
          ScaleMath: "Umeri vso matematiko (v primerjavi z okoli\u0161nim besedilom) z",
          NonZeroScale: "Merilo ne sme biti ni\u010D",
          PercentScale: "Merilo naj bo odstotek (npr. 120%%)",
          IE8warning: "To onemogo\u010Di meni MathJax in mo\u017Enosti zumiranja, vendar pa lahko namesto tega kliknete izraz ob dr\u017Eanju tipke Alt, s \u010Dimer se prika\u017Ee meni MathJax.",
          IE9warning: "Kontekstni meni MathJax bo izklopljen, namesto tega pa lahko s klikom izraza ob dr\u017Eanju tipke Alt prikli\u010Dete meni MathJax.",
          NoOriginalForm: "Na razpolago ni nobena izvorna oblika",
          Close: "Zapri",
          EqSource: "Vir ena\u010Dbe MathJax",
          FastPreview: "Hitri predogled"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/sl/MathMenu.js");
