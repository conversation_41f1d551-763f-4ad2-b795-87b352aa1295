/*************************************************************
 *
 *  MathJax/localization/ca/ca.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ca",null,{
  menuTitle: "catal\u00E0",
  version: "2.7.5",
  isLoaded: true,
  domains: {
    "_": {
        version: "2.7.5",
        isLoaded: true,
        strings: {
          CookieConfig: "MathJax ha trobat una galeta de configuraci\u00F3 d'usuari que inclou codi que s'ha d'executar. Voleu executar-lo?",
          MathProcessingError: "Error en processament d'expressi\u00F3 matem\u00E0tica",
          MathError: "Error d'expressi\u00F3 matem\u00E0tica",
          LoadFile: "Carregant %1",
          Loading: "Carregant",
          LoadFailed: "No s'ha pogut carregar el fitxer: %1",
          ProcessMath: "Processant expressi\u00F3: %1%%",
          Processing: "Processant",
          TypesetMath: "Formatejant expressi\u00F3: %1%%",
          Typesetting: "Formatejant",
          MathJaxNotSupported: "El vostre navegador no suporta MathJax",
          ErrorTips: "Consells de depuraci\u00F3: utilitzeu %%1, inspeccioneu %%2 a la consola del navegador"
        }
    },
    "FontWarnings": {},
    "HTML-CSS": {},
    "HelpDialog": {},
    "MathML": {},
    "MathMenu": {},
    "TeX": {}
  },
  plural: function (n) {
      if (n === 1) {return 1} // one
      return 2; // other
    },
  number: function (n) {
      return String(n).replace(".", ","); // replace dot by comma
    }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ca/ca.js");
