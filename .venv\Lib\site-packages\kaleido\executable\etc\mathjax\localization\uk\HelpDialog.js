/*************************************************************
 *
 *  MathJax/localization/uk/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("uk","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "MathJax \u0434\u043E\u043F\u043E\u043C\u043E\u0433\u0430",
          MathJax: "*MathJax* - \u0446\u0435 \u0431\u0456\u0431\u043B\u0456\u043E\u0442\u0435\u043A\u0430 \u043D\u0430 JavaScript, \u044F\u043A\u0430 \u0434\u043E\u0437\u0432\u043E\u043B\u044F\u0454 \u0430\u0432\u0442\u043E\u0440\u0430\u043C \u0441\u0442\u043E\u0440\u0456\u043D\u043A\u0438 \u0432\u043A\u043B\u044E\u0447\u0438\u0442\u0438 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u043A\u0443 \u043D\u0430 \u0432\u0441\u0456\u0445 \u0441\u0432\u043E\u0457\u0445 \u0432\u0435\u0431-\u0441\u0442\u043E\u0440\u0456\u043D\u043A\u0430\u0445. \u042F\u043A \u0447\u0438\u0442\u0430\u0447, \u0432\u0430\u043C \u043D\u0435 \u043F\u043E\u0442\u0440\u0456\u0431\u043D\u043E \u043D\u0456\u0447\u043E\u0433\u043E \u0440\u043E\u0431\u0438\u0442\u0438, \u0449\u043E\u0431 \u0446\u0435 \u0432\u0456\u0434\u0431\u0443\u043B\u043E\u0441\u044F.",
          Browsers: "*\u041F\u0435\u0440\u0435\u0433\u043B\u044F\u0434\u0430\u0447\u0456*: MathJax \u043F\u0440\u0430\u0446\u044E\u0454 \u0437 \u0443\u0441\u0456\u043C\u0430 \u0441\u0443\u0447\u0430\u0441\u043D\u0438\u043C\u0438 \u043F\u0435\u0440\u0435\u0433\u043B\u044F\u0434\u0430\u0447\u0430\u043C\u0438, \u0432\u043A\u043B\u044E\u0447\u0430\u044E\u0447\u0438 Internet Explorer 6+, Firefox 3+, Chrome 0.2+, Safari 2+, Opera 9.6+ \u0456 \u0431\u0456\u043B\u044C\u0448\u0456\u0441\u0442\u044C \u043C\u043E\u0431\u0456\u043B\u044C\u043D\u0438\u0445 \u043F\u0435\u0440\u0435\u0433\u043B\u044F\u0434\u0430\u0447\u0456\u0432.",
          Menu: "*\u041C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043D\u0435 \u043C\u0435\u043D\u044E*: MathJax \u0434\u043E\u0434\u0430\u0454 \u043A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u043D\u0435 \u043C\u0435\u043D\u044E \u0434\u043E \u0440\u0456\u0432\u043D\u044F\u043D\u044C.  \u041A\u043B\u0430\u0446\u0430\u043D\u043D\u044F \u043F\u0440\u0430\u0432\u043E\u044E \u043A\u043D\u043E\u043F\u043A\u043E\u044E \u043C\u0438\u0448\u0456 \u0430\u0431\u043E CTRL+\u043A\u043B\u0430\u0446\u0430\u043D\u043D\u044F \u043B\u0456\u0432\u043E\u044E \u043D\u0430 \u0431\u0443\u0434\u044C-\u044F\u043A\u043E\u043C\u0443 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043D\u043E\u043C\u0443 \u0432\u0438\u0440\u0430\u0437\u0456 \u0432\u0456\u0434\u043A\u0440\u0438\u0432\u0430\u0454 \u0434\u043E\u0441\u0442\u0443\u043F \u0434\u043E \u0446\u044C\u043E\u0433\u043E \u043C\u0435\u043D\u044E.",
          ShowMath: "*\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u0438 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u043A\u0443 \u044F\u043A* \u0434\u043E\u0437\u0432\u043E\u043B\u044F\u0454 \u0432\u0430\u043C \u043F\u0435\u0440\u0435\u0433\u043B\u044F\u043D\u0443\u0442\u0438 \u043E\u0440\u0438\u0433\u0456\u043D\u0430\u043B\u044C\u043D\u0438\u0439 \u043A\u043E\u0434 \u0444\u043E\u0440\u043C\u0443\u043B\u0438 \u0434\u043B\u044F \u043A\u043E\u043F\u0456\u044E\u0432\u0430\u043D\u043D\u044F \u0442\u0430 \u0432\u0441\u0442\u0430\u0432\u043A\u0438 (\u044F\u043A MathML \u0430\u0431\u043E \u0443 \u0457\u0457 \u0432\u043B\u0430\u0441\u043D\u043E\u043C\u0443 \u0444\u043E\u0440\u043C\u0430\u0442\u0456).",
          Settings: "*\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F* \u0434\u0430\u044E\u0442\u044C \u0432\u0430\u043C \u043F\u043E\u0432\u043D\u0438\u0439 \u043A\u043E\u043D\u0442\u0440\u043E\u043B\u044C \u043D\u0430\u0434 \u0444\u0443\u043D\u043A\u0446\u0456\u044F\u043C\u0438  MathJax \u0442\u0430\u043A\u0438\u043C\u0438 \u044F\u043A \u0440\u043E\u0437\u043C\u0456\u0440 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043D\u0438\u0445 \u0432\u0438\u0440\u0430\u0437\u0456\u0432 \u0456 \u043C\u0435\u0445\u0430\u043D\u0456\u0437\u043C, \u044F\u043A\u0438\u0439 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0454\u0442\u044C\u0441\u044F \u0434\u043B\u044F \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F \u0440\u0456\u0432\u043D\u044F\u043D\u044C.",
          Language: "*\u041C\u043E\u0432\u0430* \u0434\u043E\u0437\u0432\u043E\u043B\u044F\u0454 \u0432\u0430\u043C \u0432\u0438\u0431\u0440\u0430\u0442\u0438 \u043C\u043E\u0432\u0443 \u0434\u043B\u044F \u043C\u0435\u043D\u044E \u0442\u0430 \u043F\u043E\u043F\u0435\u0440\u0435\u0434\u0436\u0435\u043D\u044C MathJax.",
          Zoom: "*\u041C\u0430\u0441\u0448\u0442\u0430\u0431\u0443\u0432\u0430\u043D\u043D\u044F \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u043A\u0438*: \u044F\u043A\u0449\u043E \u0443 \u0432\u0430\u0441 \u0432\u0438\u043D\u0438\u043A\u043B\u0438 \u043F\u0440\u043E\u0431\u043B\u0435\u043C\u0438 \u043F\u0440\u0438 \u0447\u0438\u0442\u0430\u043D\u043D\u0456 \u0440\u0456\u0432\u043D\u044F\u043D\u043D\u044F, MathJax \u043C\u043E\u0436\u0435 \u0437\u0431\u0456\u043B\u044C\u0448\u0438\u0442\u0438 \u0457\u0445, \u0449\u043E\u0431 \u0432\u0438 \u043C\u043E\u0433\u043B\u0438 \u043F\u043E\u0431\u0430\u0447\u0438\u0442\u0438 \u0457\u0445 \u043A\u0440\u0430\u0449\u0435.",
          Accessibilty: "*\u0414\u043E\u0441\u0442\u0443\u043F\u043D\u0456\u0441\u0442\u044C*: MathJax \u0431\u0443\u0434\u0435 \u0430\u0432\u0442\u043E\u043C\u0430\u0442\u0438\u0447\u043D\u043E \u043F\u0440\u0430\u0446\u044E\u0432\u0430\u0442\u0438 \u0437 \u043F\u0440\u043E\u0433\u0440\u0430\u043C\u0430\u043C\u0438 \u0447\u0438\u0442\u0430\u043D\u043D\u044F \u0435\u043A\u0440\u0430\u043D\u0443, \u0430\u0431\u0438 \u0437\u0440\u043E\u0431\u0438\u0442\u0438 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u043A\u0443 \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u043E\u044E \u0434\u043B\u044F \u043D\u0435\u0437\u0440\u044F\u0447\u0438\u0445.",
          Fonts: "*\u0428\u0440\u0438\u0444\u0442\u0438*: MathJax \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0454 \u0441\u043F\u0435\u0446\u0456\u0430\u043B\u044C\u043D\u0456 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043D\u0456 \u0448\u0440\u0438\u0444\u0442\u0438, \u044F\u043A\u0449\u043E \u0432\u043E\u043D\u0438 \u0432\u0441\u0442\u0430\u043D\u043E\u0432\u043B\u0435\u043D\u0456 \u043D\u0430 \u0432\u0430\u0448\u043E\u043C\u0443 \u043A\u043E\u043C\u043F'\u044E\u0442\u0435\u0440\u0456. \u0412 \u0456\u043D\u0448\u043E\u043C\u0443 \u0432\u0438\u043F\u0430\u0434\u043A\u0443 \u0432\u0435\u0431-\u0448\u0440\u0438\u0444\u0442\u0438. \u0425\u043E\u0447\u0430 \u0446\u0435 \u0456 \u043D\u0435 \u0454 \u043E\u0431\u043E\u0432'\u044F\u0437\u043A\u043E\u0432\u0438\u043C,  \u043B\u043E\u043A\u0430\u043B\u044C\u043D\u043E \u0432\u0441\u0442\u0430\u043D\u043E\u0432\u043B\u0435\u043D\u0456 \u0448\u0440\u0438\u0444\u0442\u0438 \u043F\u0440\u0438\u0441\u043A\u043E\u0440\u044F\u0442\u044C \u0432\u0435\u0440\u0441\u0442\u043A\u0443. \u041C\u0438 \u0440\u0435\u043A\u043E\u043C\u0435\u043D\u0434\u0443\u0454\u043C\u043E \u0432\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0438 [STIX-\u0448\u0440\u0438\u0444\u0442\u0438](%1).",
          CloseDialog: "\u0417\u0430\u043A\u0440\u0438\u0442\u0438 \u0434\u0456\u0430\u043B\u043E\u0433 \u0434\u043E\u0432\u0456\u0434\u043A\u0438"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/uk/HelpDialog.js");
