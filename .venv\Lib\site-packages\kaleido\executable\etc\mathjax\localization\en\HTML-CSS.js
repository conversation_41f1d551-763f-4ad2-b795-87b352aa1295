/*************************************************************
 *
 *  MathJax/localization/en/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("en","HTML-CSS",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          LoadWebFont: "Loading web font %1",
          CantLoadWebFont: "Cannot load web font %1",
          FirefoxCantLoadWebFont: "Firefox cannot load web fonts from a remote host",
          CantFindFontUsing: "Cannot find a valid font using %1",
          WebFontsNotAvailable: "Web fonts not available. Using image fonts instead"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/en/HTML-CSS.js");
