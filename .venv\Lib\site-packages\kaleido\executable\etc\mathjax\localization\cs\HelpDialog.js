/*************************************************************
 *
 *  MathJax/localization/cs/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("cs","HelpDialog",{
        version: "2.7.5",
        isLoaded: true,
        strings: {
          Help: "N\u00E1pov\u011Bda k MathJaxu",
          MathJax: "*MathJax* je JavaScriptov\u00E1 knihovna, kter\u00E1 autor\u016Fm str\u00E1nek umo\u017E\u0148uje zahrnout do str\u00E1nek matematiku. Jako \u010Dten\u00E1\u0159 se nemus\u00EDte o nic starat.",
          Browsers: "*Prohl\u00ED\u017Ee\u010De*: MathJax pracuje na v\u0161ech modern\u00EDch prohl\u00ED\u017Ee\u010D\u00EDch v\u010Detn\u011B IE6+, Firefox 3+, Chrome 0.2+, Safari 2+, Opera 9.6+ a v\u011Bt\u0161in\u011B mobiln\u00EDch prohl\u00ED\u017Ee\u010D\u016F.",
          Menu: "*Matematick\u00E9 menu*: MathJax p\u0159id\u00E1v\u00E1 k rovnic\u00EDm kontextov\u00E9 menu. Pro zobrazen\u00ED menu klikn\u011Bte prav\u00FDm tla\u010D\u00EDtkem my\u0161i nebo s podr\u017Een\u00EDm kl\u00E1vesy Ctrl na jakoukoli matematiku.",
          ShowMath: "*Zobrazit matematiku jako* v\u00E1m umo\u017En\u00ED prohl\u00E9dnout si zdrojov\u00FD k\u00F3d vzorce pro okop\u00EDrov\u00E1n\u00ED (jako MathML nebo v p\u016Fvodn\u00EDm form\u00E1tu).",
          Settings: "*Nastaven\u00ED* v\u00E1m umo\u017E\u0148uj\u00ED p\u0159izp\u016Fsobit si n\u011Bkter\u00E9 funkce MathJaxu, jako jsou velikost matematiky \u010Di mechanismy pou\u017E\u00EDvan\u00E9 k zobrazov\u00E1n\u00ED rovnic.",
          Language: "*Jazyk* v\u00E1m umo\u017En\u00ED zvolit si jazyk pou\u017E\u00EDvan\u00FD MathJaxem pro menu a chybov\u00E1 hl\u00E1\u0161en\u00ED.",
          Zoom: "*Matematick\u00FD zoom*: Pokud v\u00E1m d\u011Bl\u00E1 probl\u00E9my \u010Dten\u00ED rovnice, Mathjax v\u00E1m umo\u017En\u00ED ji zv\u011Bt\u0161it, abyste ji l\u00E9pe vid\u011Bli.",
          Accessibilty: "*P\u0159\u00EDstupnost*: MathJax automaticky funguje s \u010Dte\u010Dkami obrazovky, aby matematiku zp\u0159\u00EDstupnil zrakov\u011B posti\u017Een\u00FDm.",
          Fonts: "*P\u00EDsma*: MathJax bude pou\u017E\u00EDvat jist\u00E1 matematick\u00E1 p\u00EDsma, pokud je m\u00E1te na po\u010D\u00EDta\u010Di nainstalovan\u00E9; v opa\u010Dn\u00E9m p\u0159\u00EDpad\u011B pou\u017Eije p\u00EDsma webov\u00E1. P\u0159esto\u017Ee to nen\u00ED nutn\u00E9, lok\u00E1ln\u011B nainstalovan\u00E1 p\u00EDsma urychl\u00ED sazbu. Doporu\u010Dujeme nainstalovat [p\u00EDsma STIX](%1).",
          CloseDialog: "Zav\u0159\u00EDt okno s n\u00E1pov\u011Bdou"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/cs/HelpDialog.js");
